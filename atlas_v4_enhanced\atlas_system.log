2025-07-13 16:25:24 [INFO] atlas_logging:227 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 16:25:24 [INFO] atlas_logging:228 - setup_atlas_logging(): Log level: INFO
2025-07-13 16:25:24 [INFO] atlas_logging:229 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 16:25:24 [INFO] atlas_startup_test:298 - ensure_windows_compatible_logging(): Windows-compatible logging initialized successfully
2025-07-13 16:25:24 [INFO] atlas_startup_init: A.T.L.A.S. startup initialization completed - Windows-compatible logging active
2025-07-13 16:25:24 [INFO] atlas_startup_init: Unicode encoding issues should now be resolved
2025-07-13 16:25:24 [INFO] atlas_orchestrator: [INIT] atlas_orchestrator component logging initialized
2025-07-13 16:25:24 [INFO] atlas_server: Initializing AtlasOrchestrator...
2025-07-13 16:25:24 [INFO] atlas_error_handler: [SHIELD] Enhanced <PERSON>rro<PERSON> Hand<PERSON> initialized
2025-07-13 16:25:24 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for ai_engine
2025-07-13 16:25:24 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for market_engine
2025-07-13 16:25:24 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for database
2025-07-13 16:25:24 [INFO] atlas_error_handler: [OK] Component recovery strategies initialized
2025-07-13 16:25:24 [INFO] atlas_orchestrator: [SHIELD] Enhanced error handling initialized
2025-07-13 16:25:24 [INFO] atlas_orchestrator: AtlasOrchestrator created - components will load on demand
2025-07-13 16:25:24 [INFO] atlas_orchestrator: Starting comprehensive system initialization...
2025-07-13 16:25:24 [INFO] atlas_database_manager: Database manager created - main: atlas.db, enhanced: 6 databases
2025-07-13 16:25:25 [INFO] atlas_database_manager: Main database schema created: atlas.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Database 'main' initialized: atlas.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Memory database schema created: atlas_memory.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Database 'memory' initialized: atlas_memory.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: RAG database schema created: atlas_rag.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Database 'rag' initialized: atlas_rag.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Compliance database schema created: atlas_compliance.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Database 'compliance' initialized: atlas_compliance.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Feedback database schema created: atlas_feedback.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Database 'feedback' initialized: atlas_feedback.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Enhanced memory database schema created: atlas_enhanced_memory.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: Database 'enhanced_memory' initialized: atlas_enhanced_memory.db
2025-07-13 16:25:25 [INFO] atlas_database_manager: All 6 databases initialized successfully
2025-07-13 16:25:25 [INFO] atlas_orchestrator: Database manager initialized
2025-07-13 16:25:25 [INFO] atlas_ai_engine: [INIT] atlas_ai_engine component logging initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: Predicto AI Engine created - Stock analysis expertise ready
2025-07-13 16:25:31 [INFO] atlas_ai_engine: [AI] Initializing Predicto core components...
2025-07-13 16:25:31 [INFO] atlas_ultimate_100_percent_enforcer: ATLASUltimate100PercentEnforcer initialized - Institutional standards active
2025-07-13 16:25:31 [INFO] atlas_system: [AI] Predicto Engine created - Stock Market Guru mode ready
2025-07-13 16:25:31 [INFO] atlas_system: [LINK] Predicto OpenAI client connected
2025-07-13 16:25:31 [INFO] atlas_system: [OK] Predicto OpenAI client initialized
2025-07-13 16:25:31 [INFO] atlas_system: [AI] Predicto Engine fully initialized - Ready for stock analysis conversations
2025-07-13 16:25:31 [INFO] atlas_ai_engine: [OK] Predicto Conversational Engine initialized
2025-07-13 16:25:31 [INFO] atlas_stock_intelligence_hub: [BRAIN] Stock Intelligence Hub created
2025-07-13 16:25:31 [INFO] atlas_stock_intelligence_hub.TechnicalAnalysis: [OK] Technical Analysis Module initialized
2025-07-13 16:25:31 [INFO] atlas_stock_intelligence_hub.SentimentAnalysis: [OK] Sentiment Analysis Module initialized
2025-07-13 16:25:31 [INFO] atlas_stock_intelligence_hub.PredictionEngine: [OK] Prediction Engine Module initialized
2025-07-13 16:25:31 [INFO] atlas_stock_intelligence_hub.MarketIntelligence: [OK] Market Intelligence Module initialized
2025-07-13 16:25:31 [INFO] atlas_stock_intelligence_hub: [OK] Stock Intelligence Hub fully initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: [OK] Stock Intelligence Hub initialized
2025-07-13 16:25:31 [INFO] atlas_unified_access_layer: [WEB] Unified System Access Layer created
2025-07-13 16:25:31 [INFO] atlas_unified_access_layer: [WEB] Initializing access to 25 A.T.L.A.S features
2025-07-13 16:25:31 [INFO] atlas_unified_access_layer: [OK] Unified System Access Layer fully initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: [OK] Unified System Access Layer initialized
2025-07-13 16:25:31 [INFO] atlas_conversation_flow_manager: [CHAT] Conversation Flow Manager created
2025-07-13 16:25:31 [INFO] atlas_conversation_flow_manager.SuggestionEngine: [OK] Conversation Suggestion Engine initialized
2025-07-13 16:25:31 [INFO] atlas_conversation_flow_manager: [OK] Conversation Flow Manager fully initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: [OK] Conversation Flow Manager initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: [AI] Predicto core components fully initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: Predicto core components initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: OpenAI client connected
2025-07-13 16:25:31 [INFO] atlas_ai_engine: OpenAI client initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: Technical Analysis Agent initialized with ML enhancement
2025-07-13 16:25:31 [INFO] atlas_ai_engine: Risk Management Agent initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: Sentiment Analysis Agent initialized with basic sentiment analysis
2025-07-13 16:25:31 [INFO] atlas_ai_engine: Execution Agent initialized
2025-07-13 16:25:31 [INFO] atlas_ai_engine: [OK] Initialized 4 AI agents with ML capabilities
2025-07-13 16:25:31 [INFO] atlas_ai_engine: Initializing enhanced AI components...
2025-07-13 16:25:31 [INFO] atlas_performance_optimizer: [LAUNCH] Performance Optimizer initialized
2025-07-13 16:25:31 [INFO] atlas_sentiment_analyzer: [THEATER] Sentiment Analyzer initialized - ML: True
2025-07-13 16:25:31 [INFO] atlas_ml_predictor: [AI] LSTM Predictor initialized - ML: False
2025-07-13 16:25:31 [INFO] atlas_sentiment_analyzer: [TOOL] Loading DistilBERT sentiment model...
2025-07-13 16:25:32 [INFO] atlas_sentiment_analyzer: [OK] DistilBERT sentiment model loaded successfully
2025-07-13 16:25:32 [INFO] atlas_ml_predictor: [AI] LSTM predictor running in fallback mode (no ML models)
2025-07-13 16:25:32 [INFO] atlas_ai_engine: ML components initialized
2025-07-13 16:25:32 [INFO] atlas_options_engine: [TARGET] Options Engine initialized - enabled: True
2025-07-13 16:25:32 [INFO] atlas_options_flow_analyzer: [DATA] Options Flow Analyzer initialized - enabled: True, ML: False
2025-07-13 16:25:32 [INFO] atlas_ai_engine: Options components initialized
2025-07-13 16:25:32 [INFO] atlas_portfolio_optimizer: [UP] Portfolio Optimizer initialized - enabled: True
2025-07-13 16:25:32 [INFO] atlas_ai_engine: Portfolio optimizer initialized
2025-07-13 16:25:32 [INFO] atlas_market_context: [WEB] Market Context Engine initialized - enabled: True
2025-07-13 16:25:32 [INFO] atlas_ai_engine: Market context engine initialized
2025-07-13 16:25:32 [INFO] atlas_proactive_assistant: [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 16:25:32 [INFO] atlas_ai_engine: Proactive assistant initialized
2025-07-13 16:25:32 [INFO] atlas_ai_engine: Enhanced AI components initialization complete
2025-07-13 16:25:32 [INFO] atlas_ai_engine: Predicto AI Engine fully initialized - Stock analysis expertise ready
2025-07-13 16:25:32 [INFO] atlas_orchestrator: AI engine initialized
2025-07-13 16:25:32 [INFO] atlas_market_engine: [INIT] atlas_market_engine component logging initialized
2025-07-13 16:25:32 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite initialized with 20+ scanners
2025-07-13 16:25:32 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite available
2025-07-13 16:25:32 [INFO] atlas_market_engine: [SEARCH] Web search configured - Primary: None
2025-07-13 16:25:32 [INFO] atlas_market_engine: [DATA] Market Engine created - API clients will load on demand
2025-07-13 16:25:32 [INFO] atlas_market_engine: [OK] FMP session initialized
2025-07-13 16:25:32 [INFO] atlas_trading_engine: [INIT] atlas_trading_engine component logging initialized
2025-07-13 16:25:32 [INFO] atlas_trading_engine: [TRADE] Trading Engine created - Alpaca client will load on demand
2025-07-13 16:25:32 [INFO] atlas_trading_engine: [OK] Paper trading mode enabled
2025-07-13 16:25:32 [INFO] atlas_trading_engine: [DATA] Loading paper trading positions from database
2025-07-13 16:25:32 [INFO] atlas_trading_engine: [OK] Trading Engine initialization completed
2025-07-13 16:25:32 [INFO] atlas_orchestrator: Trading engine initialized
2025-07-13 16:25:32 [INFO] atlas_risk_engine: [INIT] atlas_risk_engine component logging initialized
2025-07-13 16:25:32 [INFO] atlas_risk_engine: [SHIELD] Risk Engine created - safety guardrails active
2025-07-13 16:25:32 [INFO] atlas_risk_engine: [DATA] Risk parameters loaded - Max position size: 2.0%
2025-07-13 16:25:32 [INFO] atlas_risk_engine: [SEARCH] Risk monitoring systems initialized
2025-07-13 16:25:32 [INFO] atlas_risk_engine: [OK] Risk Engine initialization completed
2025-07-13 16:25:32 [INFO] atlas_orchestrator: Risk engine initialized
2025-07-13 16:25:32 [INFO] atlas_education_engine: [INIT] atlas_education_engine component logging initialized
2025-07-13 16:25:32 [INFO] atlas_education_engine: [LIBRARY] Education Engine created - ChromaDB will load on demand
2025-07-13 16:25:32 [INFO] atlas_education_engine: [BOOK] Basic educational content loaded
2025-07-13 16:25:32 [INFO] atlas_education_engine: [OK] Education Engine initialization completed
2025-07-13 16:25:32 [INFO] atlas_orchestrator: Education engine initialized
2025-07-13 16:25:32 [INFO] atlas_ai_engine: DistilBERT sentiment model loaded
2025-07-13 16:25:33 [INFO] atlas_market_engine: [OK] FMP API connection tested successfully
2025-07-13 16:25:33 [INFO] atlas_market_engine: [INFO] Predicto API configured with placeholder URL - skipping connection test
2025-07-13 16:25:33 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite connected to market engine
2025-07-13 16:25:33 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite integrated
2025-07-13 16:25:33 [INFO] atlas_market_engine: [OK] Market Engine initialization completed
2025-07-13 16:25:33 [INFO] atlas_orchestrator: Market engine initialized
2025-07-13 16:25:33 [INFO] atlas_orchestrator: System initialization completed
2025-07-13 16:25:33 [INFO] atlas_server: A.T.L.A.S system initialization completed successfully
2025-07-13 16:33:01 [INFO] atlas_logging:227 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 16:33:01 [INFO] atlas_logging:228 - setup_atlas_logging(): Log level: INFO
2025-07-13 16:33:01 [INFO] atlas_logging:229 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 16:33:01 [INFO] atlas_startup_test:298 - ensure_windows_compatible_logging(): Windows-compatible logging initialized successfully
2025-07-13 16:33:01 [INFO] atlas_startup_init: A.T.L.A.S. startup initialization completed - Windows-compatible logging active
2025-07-13 16:33:01 [INFO] atlas_startup_init: Unicode encoding issues should now be resolved
2025-07-13 16:33:01 [INFO] atlas_orchestrator: [INIT] atlas_orchestrator component logging initialized
2025-07-13 16:33:01 [INFO] atlas_server: Initializing AtlasOrchestrator...
2025-07-13 16:33:01 [INFO] atlas_error_handler: [SHIELD] Enhanced Error Handler initialized
2025-07-13 16:33:01 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for ai_engine
2025-07-13 16:33:01 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for market_engine
2025-07-13 16:33:01 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for database
2025-07-13 16:33:01 [INFO] atlas_error_handler: [OK] Component recovery strategies initialized
2025-07-13 16:33:01 [INFO] atlas_orchestrator: [SHIELD] Enhanced error handling initialized
2025-07-13 16:33:01 [INFO] atlas_orchestrator: AtlasOrchestrator created - components will load on demand
2025-07-13 16:33:01 [INFO] atlas_orchestrator: Starting comprehensive system initialization...
2025-07-13 16:33:01 [INFO] atlas_database_manager: Database manager created - main: atlas.db, enhanced: 6 databases
2025-07-13 16:33:01 [INFO] atlas_database_manager: Main database schema created: atlas.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Database 'main' initialized: atlas.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Memory database schema created: atlas_memory.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Database 'memory' initialized: atlas_memory.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: RAG database schema created: atlas_rag.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Database 'rag' initialized: atlas_rag.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Compliance database schema created: atlas_compliance.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Database 'compliance' initialized: atlas_compliance.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Feedback database schema created: atlas_feedback.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Database 'feedback' initialized: atlas_feedback.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Enhanced memory database schema created: atlas_enhanced_memory.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: Database 'enhanced_memory' initialized: atlas_enhanced_memory.db
2025-07-13 16:33:01 [INFO] atlas_database_manager: All 6 databases initialized successfully
2025-07-13 16:33:01 [INFO] atlas_orchestrator: Database manager initialized
2025-07-13 16:33:01 [INFO] atlas_ai_engine: [INIT] atlas_ai_engine component logging initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: Predicto AI Engine created - Stock analysis expertise ready
2025-07-13 16:33:05 [INFO] atlas_ai_engine: [AI] Initializing Predicto core components...
2025-07-13 16:33:05 [INFO] atlas_ultimate_100_percent_enforcer: ATLASUltimate100PercentEnforcer initialized - Institutional standards active
2025-07-13 16:33:05 [INFO] atlas_system: [AI] Predicto Engine created - Stock Market Guru mode ready
2025-07-13 16:33:05 [INFO] atlas_system: [LINK] Predicto OpenAI client connected
2025-07-13 16:33:05 [INFO] atlas_system: [OK] Predicto OpenAI client initialized
2025-07-13 16:33:05 [INFO] atlas_system: [AI] Predicto Engine fully initialized - Ready for stock analysis conversations
2025-07-13 16:33:05 [INFO] atlas_ai_engine: [OK] Predicto Conversational Engine initialized
2025-07-13 16:33:05 [INFO] atlas_stock_intelligence_hub: [BRAIN] Stock Intelligence Hub created
2025-07-13 16:33:05 [INFO] atlas_stock_intelligence_hub.TechnicalAnalysis: [OK] Technical Analysis Module initialized
2025-07-13 16:33:05 [INFO] atlas_stock_intelligence_hub.SentimentAnalysis: [OK] Sentiment Analysis Module initialized
2025-07-13 16:33:05 [INFO] atlas_stock_intelligence_hub.PredictionEngine: [OK] Prediction Engine Module initialized
2025-07-13 16:33:05 [INFO] atlas_stock_intelligence_hub.MarketIntelligence: [OK] Market Intelligence Module initialized
2025-07-13 16:33:05 [INFO] atlas_stock_intelligence_hub: [OK] Stock Intelligence Hub fully initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: [OK] Stock Intelligence Hub initialized
2025-07-13 16:33:05 [INFO] atlas_unified_access_layer: [WEB] Unified System Access Layer created
2025-07-13 16:33:05 [INFO] atlas_unified_access_layer: [WEB] Initializing access to 25 A.T.L.A.S features
2025-07-13 16:33:05 [INFO] atlas_unified_access_layer: [OK] Unified System Access Layer fully initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: [OK] Unified System Access Layer initialized
2025-07-13 16:33:05 [INFO] atlas_conversation_flow_manager: [CHAT] Conversation Flow Manager created
2025-07-13 16:33:05 [INFO] atlas_conversation_flow_manager.SuggestionEngine: [OK] Conversation Suggestion Engine initialized
2025-07-13 16:33:05 [INFO] atlas_conversation_flow_manager: [OK] Conversation Flow Manager fully initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: [OK] Conversation Flow Manager initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: [AI] Predicto core components fully initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: Predicto core components initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: OpenAI client connected
2025-07-13 16:33:05 [INFO] atlas_ai_engine: OpenAI client initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: Technical Analysis Agent initialized with ML enhancement
2025-07-13 16:33:05 [INFO] atlas_ai_engine: Risk Management Agent initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: Sentiment Analysis Agent initialized with basic sentiment analysis
2025-07-13 16:33:05 [INFO] atlas_ai_engine: Execution Agent initialized
2025-07-13 16:33:05 [INFO] atlas_ai_engine: [OK] Initialized 4 AI agents with ML capabilities
2025-07-13 16:33:05 [INFO] atlas_ai_engine: Initializing enhanced AI components...
2025-07-13 16:33:05 [INFO] atlas_performance_optimizer: [LAUNCH] Performance Optimizer initialized
2025-07-13 16:33:05 [INFO] atlas_sentiment_analyzer: [THEATER] Sentiment Analyzer initialized - ML: True
2025-07-13 16:33:05 [INFO] atlas_ml_predictor: [AI] LSTM Predictor initialized - ML: False
2025-07-13 16:33:05 [INFO] atlas_sentiment_analyzer: [TOOL] Loading DistilBERT sentiment model...
2025-07-13 16:33:06 [INFO] atlas_sentiment_analyzer: [OK] DistilBERT sentiment model loaded successfully
2025-07-13 16:33:06 [INFO] atlas_ml_predictor: [AI] LSTM predictor running in fallback mode (no ML models)
2025-07-13 16:33:06 [INFO] atlas_ai_engine: ML components initialized
2025-07-13 16:33:06 [INFO] atlas_options_engine: [TARGET] Options Engine initialized - enabled: True
2025-07-13 16:33:06 [INFO] atlas_options_flow_analyzer: [DATA] Options Flow Analyzer initialized - enabled: True, ML: False
2025-07-13 16:33:06 [INFO] atlas_ai_engine: Options components initialized
2025-07-13 16:33:06 [INFO] atlas_portfolio_optimizer: [UP] Portfolio Optimizer initialized - enabled: True
2025-07-13 16:33:06 [INFO] atlas_ai_engine: Portfolio optimizer initialized
2025-07-13 16:33:06 [INFO] atlas_market_context: [WEB] Market Context Engine initialized - enabled: True
2025-07-13 16:33:06 [INFO] atlas_ai_engine: Market context engine initialized
2025-07-13 16:33:06 [INFO] atlas_proactive_assistant: [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 16:33:06 [INFO] atlas_ai_engine: Proactive assistant initialized
2025-07-13 16:33:06 [INFO] atlas_ai_engine: Enhanced AI components initialization complete
2025-07-13 16:33:06 [INFO] atlas_ai_engine: Predicto AI Engine fully initialized - Stock analysis expertise ready
2025-07-13 16:33:06 [INFO] atlas_orchestrator: AI engine initialized
2025-07-13 16:33:06 [INFO] atlas_market_engine: [INIT] atlas_market_engine component logging initialized
2025-07-13 16:33:06 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite initialized with 20+ scanners
2025-07-13 16:33:06 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite available
2025-07-13 16:33:06 [INFO] atlas_market_engine: [SEARCH] Web search configured - Primary: None
2025-07-13 16:33:06 [INFO] atlas_market_engine: [DATA] Market Engine created - API clients will load on demand
2025-07-13 16:33:06 [INFO] atlas_market_engine: [OK] FMP session initialized
2025-07-13 16:33:06 [INFO] atlas_trading_engine: [INIT] atlas_trading_engine component logging initialized
2025-07-13 16:33:06 [INFO] atlas_trading_engine: [TRADE] Trading Engine created - Alpaca client will load on demand
2025-07-13 16:33:06 [INFO] atlas_trading_engine: [OK] Paper trading mode enabled
2025-07-13 16:33:06 [INFO] atlas_trading_engine: [DATA] Loading paper trading positions from database
2025-07-13 16:33:06 [INFO] atlas_trading_engine: [OK] Trading Engine initialization completed
2025-07-13 16:33:06 [INFO] atlas_orchestrator: Trading engine initialized
2025-07-13 16:33:06 [INFO] atlas_risk_engine: [INIT] atlas_risk_engine component logging initialized
2025-07-13 16:33:06 [INFO] atlas_risk_engine: [SHIELD] Risk Engine created - safety guardrails active
2025-07-13 16:33:06 [INFO] atlas_risk_engine: [DATA] Risk parameters loaded - Max position size: 2.0%
2025-07-13 16:33:06 [INFO] atlas_risk_engine: [SEARCH] Risk monitoring systems initialized
2025-07-13 16:33:06 [INFO] atlas_risk_engine: [OK] Risk Engine initialization completed
2025-07-13 16:33:06 [INFO] atlas_orchestrator: Risk engine initialized
2025-07-13 16:33:06 [INFO] atlas_education_engine: [INIT] atlas_education_engine component logging initialized
2025-07-13 16:33:06 [INFO] atlas_education_engine: [LIBRARY] Education Engine created - ChromaDB will load on demand
2025-07-13 16:33:06 [INFO] atlas_education_engine: [BOOK] Basic educational content loaded
2025-07-13 16:33:06 [INFO] atlas_education_engine: [OK] Education Engine initialization completed
2025-07-13 16:33:06 [INFO] atlas_orchestrator: Education engine initialized
2025-07-13 16:33:06 [INFO] atlas_ai_engine: DistilBERT sentiment model loaded
2025-07-13 16:33:07 [INFO] atlas_market_engine: [OK] FMP API connection tested successfully
2025-07-13 16:33:07 [INFO] atlas_market_engine: [INFO] Predicto API configured with placeholder URL - skipping connection test
2025-07-13 16:33:07 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite connected to market engine
2025-07-13 16:33:07 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite integrated
2025-07-13 16:33:07 [INFO] atlas_market_engine: [OK] Market Engine initialization completed
2025-07-13 16:33:07 [INFO] atlas_orchestrator: Market engine initialized
2025-07-13 16:33:07 [INFO] atlas_orchestrator: System initialization completed
2025-07-13 16:33:07 [INFO] atlas_server: A.T.L.A.S system initialization completed successfully
2025-07-13 16:33:29 [INFO] atlas_server: Predicto processing: What is a stock?...
2025-07-13 16:33:29 [INFO] atlas_orchestrator: Trading God Engine initialized
2025-07-13 16:33:29 [INFO] atlas_ai_engine: [AI] Processing message through Predicto: What is a stock?...
2025-07-13 16:33:29 [INFO] atlas_system: [TARGET] Detected trading request: symbols=False, trading_intent=False, analysis=True
2025-07-13 16:33:29 [INFO] atlas_system: [TARGET] Determined persona: guru - routing to appropriate handler
2025-07-13 16:33:29 [INFO] atlas_system: [AI] Using Stock Market Guru mode - sophisticated 6-point format
2025-07-13 16:33:29 [INFO] atlas_system: Dynamic position sizing for GOOGL:
2025-07-13 16:33:29 [INFO] atlas_system:   Account balance: $100,000
2025-07-13 16:33:29 [INFO] atlas_system:   Entry price: $175.25
2025-07-13 16:33:29 [INFO] atlas_system:   Risk per share: $3.50
2025-07-13 16:33:29 [INFO] atlas_system:   Max risk amount: $2,000
2025-07-13 16:33:29 [INFO] atlas_system:   Risk-based quantity: 571
2025-07-13 16:33:29 [INFO] atlas_system:   Allocation-based quantity: 57
2025-07-13 16:33:29 [INFO] atlas_system:   Final quantity: 57
2025-07-13 16:33:29 [INFO] atlas_system:   Position value: $9,989
2025-07-13 16:33:29 [INFO] atlas_system:   Risk amount: $200
2025-07-13 16:33:29 [INFO] atlas_system:   Portfolio allocation: 10.0%
2025-07-13 16:33:29 [INFO] atlas_system:   Portfolio risk: 0.20%
2025-07-13 16:33:29 [INFO] atlas_system: [FLOPPY] Cached trade plan ******** (expires at 16:48:29)
2025-07-13 16:33:29 [INFO] atlas_system: [TARGET] Switched to Stock Market Guru persona
2025-07-13 16:33:29 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-13 16:33:29 [WARNING] atlas_system: Failed to generate dynamic guru response: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-proj-********************************************************************************************************************************************************fLcA. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-13 16:33:40 [INFO] atlas_server: Predicto processing: I'm a complete beginner. Can you explain what a stock is in simple terms?...
2025-07-13 16:33:40 [INFO] atlas_ai_engine: [AI] Processing message through Predicto: I'm a complete beginner. Can you explain what a stock is in simple terms?...
2025-07-13 16:33:40 [INFO] atlas_system: [TALK] Detected conversational message: 'I'm a complete beginner. Can you explain what a st...' - using conversational response
2025-07-13 16:33:40 [INFO] atlas_system: [TARGET] Determined persona: conversational - routing to appropriate handler
2025-07-13 16:33:40 [INFO] atlas_system: ? Using conversational mode - natural responses
2025-07-13 16:33:40 [INFO] atlas_system: ? Switched to Conversational persona
2025-07-13 16:33:40 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-13 16:33:40 [WARNING] atlas_system: Failed to generate dynamic greeting: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-proj-********************************************************************************************************************************************************fLcA. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-13 16:33:40 [INFO] atlas_orchestrator: [SHIELD] Protecting conversational response type 'greeting' from Trading God transformation
2025-07-13 16:34:23 [INFO] atlas_logging:227 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 16:34:23 [INFO] atlas_logging:228 - setup_atlas_logging(): Log level: INFO
2025-07-13 16:34:23 [INFO] atlas_logging:229 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 16:34:23 [INFO] atlas_startup_test:298 - ensure_windows_compatible_logging(): Windows-compatible logging initialized successfully
2025-07-13 16:34:23 [INFO] atlas_startup_init: A.T.L.A.S. startup initialization completed - Windows-compatible logging active
2025-07-13 16:34:23 [INFO] atlas_startup_init: Unicode encoding issues should now be resolved
2025-07-13 16:34:23 [INFO] atlas_orchestrator: [INIT] atlas_orchestrator component logging initialized
2025-07-13 16:34:23 [INFO] atlas_server: Initializing AtlasOrchestrator...
2025-07-13 16:34:23 [INFO] atlas_error_handler: [SHIELD] Enhanced Error Handler initialized
2025-07-13 16:34:23 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for ai_engine
2025-07-13 16:34:23 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for market_engine
2025-07-13 16:34:23 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for database
2025-07-13 16:34:23 [INFO] atlas_error_handler: [OK] Component recovery strategies initialized
2025-07-13 16:34:23 [INFO] atlas_orchestrator: [SHIELD] Enhanced error handling initialized
2025-07-13 16:34:23 [INFO] atlas_orchestrator: AtlasOrchestrator created - components will load on demand
2025-07-13 16:34:23 [INFO] atlas_orchestrator: Starting comprehensive system initialization...
2025-07-13 16:34:23 [INFO] atlas_database_manager: Database manager created - main: atlas.db, enhanced: 6 databases
2025-07-13 16:34:23 [INFO] atlas_database_manager: Main database schema created: atlas.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Database 'main' initialized: atlas.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Memory database schema created: atlas_memory.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Database 'memory' initialized: atlas_memory.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: RAG database schema created: atlas_rag.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Database 'rag' initialized: atlas_rag.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Compliance database schema created: atlas_compliance.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Database 'compliance' initialized: atlas_compliance.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Feedback database schema created: atlas_feedback.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Database 'feedback' initialized: atlas_feedback.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Enhanced memory database schema created: atlas_enhanced_memory.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: Database 'enhanced_memory' initialized: atlas_enhanced_memory.db
2025-07-13 16:34:23 [INFO] atlas_database_manager: All 6 databases initialized successfully
2025-07-13 16:34:23 [INFO] atlas_orchestrator: Database manager initialized
2025-07-13 16:34:23 [INFO] atlas_ai_engine: [INIT] atlas_ai_engine component logging initialized
2025-07-13 16:34:26 [INFO] atlas_ai_engine: Predicto AI Engine created - Stock analysis expertise ready
2025-07-13 16:34:26 [INFO] atlas_ai_engine: [AI] Initializing Predicto core components...
2025-07-13 16:34:26 [INFO] atlas_ultimate_100_percent_enforcer: ATLASUltimate100PercentEnforcer initialized - Institutional standards active
2025-07-13 16:34:26 [INFO] atlas_system: [AI] Predicto Engine created - Stock Market Guru mode ready
2025-07-13 16:34:27 [INFO] atlas_system: [LINK] Predicto OpenAI client connected
2025-07-13 16:34:27 [INFO] atlas_system: [OK] Predicto OpenAI client initialized
2025-07-13 16:34:27 [INFO] atlas_system: [AI] Predicto Engine fully initialized - Ready for stock analysis conversations
2025-07-13 16:34:27 [INFO] atlas_ai_engine: [OK] Predicto Conversational Engine initialized
2025-07-13 16:34:27 [INFO] atlas_stock_intelligence_hub: [BRAIN] Stock Intelligence Hub created
2025-07-13 16:34:27 [INFO] atlas_stock_intelligence_hub.TechnicalAnalysis: [OK] Technical Analysis Module initialized
2025-07-13 16:34:27 [INFO] atlas_stock_intelligence_hub.SentimentAnalysis: [OK] Sentiment Analysis Module initialized
2025-07-13 16:34:27 [INFO] atlas_stock_intelligence_hub.PredictionEngine: [OK] Prediction Engine Module initialized
2025-07-13 16:34:27 [INFO] atlas_stock_intelligence_hub.MarketIntelligence: [OK] Market Intelligence Module initialized
2025-07-13 16:34:27 [INFO] atlas_stock_intelligence_hub: [OK] Stock Intelligence Hub fully initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: [OK] Stock Intelligence Hub initialized
2025-07-13 16:34:27 [INFO] atlas_unified_access_layer: [WEB] Unified System Access Layer created
2025-07-13 16:34:27 [INFO] atlas_unified_access_layer: [WEB] Initializing access to 25 A.T.L.A.S features
2025-07-13 16:34:27 [INFO] atlas_unified_access_layer: [OK] Unified System Access Layer fully initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: [OK] Unified System Access Layer initialized
2025-07-13 16:34:27 [INFO] atlas_conversation_flow_manager: [CHAT] Conversation Flow Manager created
2025-07-13 16:34:27 [INFO] atlas_conversation_flow_manager.SuggestionEngine: [OK] Conversation Suggestion Engine initialized
2025-07-13 16:34:27 [INFO] atlas_conversation_flow_manager: [OK] Conversation Flow Manager fully initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: [OK] Conversation Flow Manager initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: [AI] Predicto core components fully initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Predicto core components initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: OpenAI client connected
2025-07-13 16:34:27 [INFO] atlas_ai_engine: OpenAI client initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Technical Analysis Agent initialized with ML enhancement
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Risk Management Agent initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Sentiment Analysis Agent initialized with basic sentiment analysis
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Execution Agent initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: [OK] Initialized 4 AI agents with ML capabilities
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Initializing enhanced AI components...
2025-07-13 16:34:27 [INFO] atlas_performance_optimizer: [LAUNCH] Performance Optimizer initialized
2025-07-13 16:34:27 [INFO] atlas_sentiment_analyzer: [THEATER] Sentiment Analyzer initialized - ML: True
2025-07-13 16:34:27 [INFO] atlas_ml_predictor: [AI] LSTM Predictor initialized - ML: False
2025-07-13 16:34:27 [INFO] atlas_sentiment_analyzer: [TOOL] Loading DistilBERT sentiment model...
2025-07-13 16:34:27 [INFO] atlas_sentiment_analyzer: [OK] DistilBERT sentiment model loaded successfully
2025-07-13 16:34:27 [INFO] atlas_ml_predictor: [AI] LSTM predictor running in fallback mode (no ML models)
2025-07-13 16:34:27 [INFO] atlas_ai_engine: ML components initialized
2025-07-13 16:34:27 [INFO] atlas_options_engine: [TARGET] Options Engine initialized - enabled: True
2025-07-13 16:34:27 [INFO] atlas_options_flow_analyzer: [DATA] Options Flow Analyzer initialized - enabled: True, ML: False
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Options components initialized
2025-07-13 16:34:27 [INFO] atlas_portfolio_optimizer: [UP] Portfolio Optimizer initialized - enabled: True
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Portfolio optimizer initialized
2025-07-13 16:34:27 [INFO] atlas_market_context: [WEB] Market Context Engine initialized - enabled: True
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Market context engine initialized
2025-07-13 16:34:27 [INFO] atlas_proactive_assistant: [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Proactive assistant initialized
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Enhanced AI components initialization complete
2025-07-13 16:34:27 [INFO] atlas_ai_engine: Predicto AI Engine fully initialized - Stock analysis expertise ready
2025-07-13 16:34:27 [INFO] atlas_orchestrator: AI engine initialized
2025-07-13 16:34:27 [INFO] atlas_market_engine: [INIT] atlas_market_engine component logging initialized
2025-07-13 16:34:27 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite initialized with 20+ scanners
2025-07-13 16:34:27 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite available
2025-07-13 16:34:27 [INFO] atlas_market_engine: [SEARCH] Web search configured - Primary: None
2025-07-13 16:34:27 [INFO] atlas_market_engine: [DATA] Market Engine created - API clients will load on demand
2025-07-13 16:34:27 [INFO] atlas_market_engine: [OK] FMP session initialized
2025-07-13 16:34:27 [INFO] atlas_trading_engine: [INIT] atlas_trading_engine component logging initialized
2025-07-13 16:34:27 [INFO] atlas_trading_engine: [TRADE] Trading Engine created - Alpaca client will load on demand
2025-07-13 16:34:27 [INFO] atlas_trading_engine: [OK] Paper trading mode enabled
2025-07-13 16:34:27 [INFO] atlas_trading_engine: [DATA] Loading paper trading positions from database
2025-07-13 16:34:27 [INFO] atlas_trading_engine: [OK] Trading Engine initialization completed
2025-07-13 16:34:27 [INFO] atlas_orchestrator: Trading engine initialized
2025-07-13 16:34:27 [INFO] atlas_risk_engine: [INIT] atlas_risk_engine component logging initialized
2025-07-13 16:34:27 [INFO] atlas_risk_engine: [SHIELD] Risk Engine created - safety guardrails active
2025-07-13 16:34:27 [INFO] atlas_risk_engine: [DATA] Risk parameters loaded - Max position size: 2.0%
2025-07-13 16:34:27 [INFO] atlas_risk_engine: [SEARCH] Risk monitoring systems initialized
2025-07-13 16:34:27 [INFO] atlas_risk_engine: [OK] Risk Engine initialization completed
2025-07-13 16:34:27 [INFO] atlas_orchestrator: Risk engine initialized
2025-07-13 16:34:27 [INFO] atlas_education_engine: [INIT] atlas_education_engine component logging initialized
2025-07-13 16:34:27 [INFO] atlas_education_engine: [LIBRARY] Education Engine created - ChromaDB will load on demand
2025-07-13 16:34:27 [INFO] atlas_education_engine: [BOOK] Basic educational content loaded
2025-07-13 16:34:27 [INFO] atlas_education_engine: [OK] Education Engine initialization completed
2025-07-13 16:34:27 [INFO] atlas_orchestrator: Education engine initialized
2025-07-13 16:34:28 [INFO] atlas_ai_engine: DistilBERT sentiment model loaded
2025-07-13 16:34:28 [INFO] atlas_market_engine: [OK] FMP API connection tested successfully
2025-07-13 16:34:28 [INFO] atlas_market_engine: [INFO] Predicto API configured with placeholder URL - skipping connection test
2025-07-13 16:34:28 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite connected to market engine
2025-07-13 16:34:28 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite integrated
2025-07-13 16:34:28 [INFO] atlas_market_engine: [OK] Market Engine initialization completed
2025-07-13 16:34:28 [INFO] atlas_orchestrator: Market engine initialized
2025-07-13 16:34:28 [INFO] atlas_orchestrator: System initialization completed
2025-07-13 16:34:28 [INFO] atlas_server: A.T.L.A.S system initialization completed successfully
2025-07-13 16:34:52 [INFO] atlas_server: Predicto processing: I'm a complete beginner. Can you explain what a stock is in simple terms?...
2025-07-13 16:34:52 [INFO] atlas_orchestrator: Trading God Engine initialized
2025-07-13 16:34:52 [INFO] atlas_ai_engine: [AI] Processing message through Predicto: I'm a complete beginner. Can you explain what a stock is in simple terms?...
2025-07-13 16:34:52 [INFO] atlas_system: [TALK] Detected conversational message: 'I'm a complete beginner. Can you explain what a st...' - using conversational response
2025-07-13 16:34:52 [INFO] atlas_system: [TARGET] Determined persona: conversational - routing to appropriate handler
2025-07-13 16:34:52 [INFO] atlas_system: ? Using conversational mode - natural responses
2025-07-13 16:34:52 [INFO] atlas_system: ? Switched to Conversational persona
2025-07-13 16:34:56 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-13 16:34:56 [INFO] atlas_orchestrator: [SHIELD] Protecting conversational response type 'greeting' from Trading God transformation
2025-07-13 16:35:04 [INFO] atlas_server: Predicto processing: Should I buy Apple stock?...
2025-07-13 16:35:04 [INFO] atlas_ai_engine: [AI] Processing message through Predicto: Should I buy Apple stock?...
2025-07-13 16:35:04 [INFO] atlas_system: [TARGET] Detected trading request: symbols=True, trading_intent=True, analysis=True
2025-07-13 16:35:04 [INFO] atlas_system: [TARGET] Determined persona: guru - routing to appropriate handler
2025-07-13 16:35:04 [INFO] atlas_system: [AI] Using Stock Market Guru mode - sophisticated 6-point format
2025-07-13 16:35:05 [ERROR] atlas_market_engine: Error getting quote for APPLE: No quote data available for APPLE
2025-07-13 16:35:05 [WARNING] atlas_system: Quote fetch failed for APPLE: No quote data available for APPLE
2025-07-13 16:35:05 [ERROR] atlas_market_engine: Error getting quote for APPLE: No quote data available for APPLE
2025-07-13 16:35:05 [WARNING] atlas_market_engine: TTM Squeeze scan error for APPLE: No quote data available for APPLE
2025-07-13 16:35:05 [INFO] atlas_system: Dynamic position sizing for APPLE:
2025-07-13 16:35:05 [INFO] atlas_system:   Account balance: $100,000
2025-07-13 16:35:05 [INFO] atlas_system:   Entry price: $175.25
2025-07-13 16:35:05 [INFO] atlas_system:   Risk per share: $3.50
2025-07-13 16:35:05 [INFO] atlas_system:   Max risk amount: $2,000
2025-07-13 16:35:05 [INFO] atlas_system:   Risk-based quantity: 570
2025-07-13 16:35:05 [INFO] atlas_system:   Allocation-based quantity: 57
2025-07-13 16:35:05 [INFO] atlas_system:   Final quantity: 57
2025-07-13 16:35:05 [INFO] atlas_system:   Position value: $9,989
2025-07-13 16:35:05 [INFO] atlas_system:   Risk amount: $200
2025-07-13 16:35:05 [INFO] atlas_system:   Portfolio allocation: 10.0%
2025-07-13 16:35:05 [INFO] atlas_system:   Portfolio risk: 0.20%
2025-07-13 16:35:05 [INFO] atlas_system: [FLOPPY] Cached trade plan 5D1A8DB5 (expires at 16:50:05)
2025-07-13 16:35:05 [INFO] atlas_system: [TARGET] Switched to Stock Market Guru persona
2025-07-13 16:35:11 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-13 16:35:21 [INFO] atlas_server: Predicto processing: What is the Lee Method?...
2025-07-13 16:35:21 [INFO] atlas_ai_engine: [AI] Processing message through Predicto: What is the Lee Method?...
2025-07-13 16:35:21 [INFO] atlas_system: [TARGET] Detected trading request: symbols=True, trading_intent=False, analysis=True
2025-07-13 16:35:21 [INFO] atlas_system: [TARGET] Determined persona: guru - routing to appropriate handler
2025-07-13 16:35:21 [INFO] atlas_system: [AI] Using Stock Market Guru mode - sophisticated 6-point format
2025-07-13 16:35:21 [INFO] atlas_system: Dynamic position sizing for LEE:
2025-07-13 16:35:21 [INFO] atlas_system:   Account balance: $100,000
2025-07-13 16:35:21 [INFO] atlas_system:   Entry price: $175.25
2025-07-13 16:35:21 [INFO] atlas_system:   Risk per share: $3.50
2025-07-13 16:35:21 [INFO] atlas_system:   Max risk amount: $2,000
2025-07-13 16:35:21 [INFO] atlas_system:   Risk-based quantity: 571
2025-07-13 16:35:21 [INFO] atlas_system:   Allocation-based quantity: 57
2025-07-13 16:35:21 [INFO] atlas_system:   Final quantity: 57
2025-07-13 16:35:21 [INFO] atlas_system:   Position value: $9,989
2025-07-13 16:35:21 [INFO] atlas_system:   Risk amount: $200
2025-07-13 16:35:21 [INFO] atlas_system:   Portfolio allocation: 10.0%
2025-07-13 16:35:21 [INFO] atlas_system:   Portfolio risk: 0.20%
2025-07-13 16:35:21 [INFO] atlas_system: [FLOPPY] Cached trade plan 4555B387 (expires at 16:50:21)
2025-07-13 16:35:21 [INFO] atlas_system: [TARGET] Switched to Stock Market Guru persona
2025-07-13 16:35:26 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-13 16:35:35 [INFO] atlas_server: Predicto processing: How much should I risk on each trade?...
2025-07-13 16:35:35 [INFO] atlas_ai_engine: [AI] Processing message through Predicto: How much should I risk on each trade?...
2025-07-13 16:35:35 [INFO] atlas_system: [TARGET] Detected trading request: symbols=True, trading_intent=True, analysis=True
2025-07-13 16:35:35 [INFO] atlas_system: [TARGET] Determined persona: guru - routing to appropriate handler
2025-07-13 16:35:35 [INFO] atlas_system: [AI] Using Stock Market Guru mode - sophisticated 6-point format
2025-07-13 16:35:35 [ERROR] atlas_market_engine: Error getting quote for MUCH: No quote data available for MUCH
2025-07-13 16:35:35 [WARNING] atlas_system: Quote fetch failed for MUCH: No quote data available for MUCH
2025-07-13 16:35:35 [ERROR] atlas_market_engine: Error getting quote for MUCH: No quote data available for MUCH
2025-07-13 16:35:35 [WARNING] atlas_market_engine: TTM Squeeze scan error for MUCH: No quote data available for MUCH
2025-07-13 16:35:35 [INFO] atlas_system: Dynamic position sizing for MUCH:
2025-07-13 16:35:35 [INFO] atlas_system:   Account balance: $100,000
2025-07-13 16:35:35 [INFO] atlas_system:   Entry price: $175.25
2025-07-13 16:35:35 [INFO] atlas_system:   Risk per share: $3.50
2025-07-13 16:35:35 [INFO] atlas_system:   Max risk amount: $2,000
2025-07-13 16:35:35 [INFO] atlas_system:   Risk-based quantity: 571
2025-07-13 16:35:35 [INFO] atlas_system:   Allocation-based quantity: 57
2025-07-13 16:35:35 [INFO] atlas_system:   Final quantity: 57
2025-07-13 16:35:35 [INFO] atlas_system:   Position value: $9,989
2025-07-13 16:35:35 [INFO] atlas_system:   Risk amount: $200
2025-07-13 16:35:35 [INFO] atlas_system:   Portfolio allocation: 10.0%
2025-07-13 16:35:35 [INFO] atlas_system:   Portfolio risk: 0.20%
2025-07-13 16:35:35 [INFO] atlas_system: [FLOPPY] Cached trade plan 35AEDA63 (expires at 16:50:35)
2025-07-13 16:35:35 [INFO] atlas_system: [TARGET] Switched to Stock Market Guru persona
2025-07-13 16:35:39 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-13 16:43:05 [INFO] atlas_logging:227 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 16:43:05 [INFO] atlas_logging:228 - setup_atlas_logging(): Log level: INFO
2025-07-13 16:43:05 [INFO] atlas_logging:229 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 16:43:05 [INFO] atlas_startup_test:298 - ensure_windows_compatible_logging(): Windows-compatible logging initialized successfully
2025-07-13 16:43:05 [INFO] atlas_startup_init: A.T.L.A.S. startup initialization completed - Windows-compatible logging active
2025-07-13 16:43:05 [INFO] atlas_startup_init: Unicode encoding issues should now be resolved
2025-07-13 16:43:05 [INFO] atlas_orchestrator: [INIT] atlas_orchestrator component logging initialized
2025-07-13 16:43:05 [INFO] atlas_server: Initializing AtlasOrchestrator...
2025-07-13 16:43:05 [INFO] atlas_error_handler: [SHIELD] Enhanced Error Handler initialized
2025-07-13 16:43:05 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for ai_engine
2025-07-13 16:43:05 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for market_engine
2025-07-13 16:43:05 [INFO] atlas_error_handler: [TOOL] Recovery strategy registered for database
2025-07-13 16:43:05 [INFO] atlas_error_handler: [OK] Component recovery strategies initialized
2025-07-13 16:43:05 [INFO] atlas_orchestrator: [SHIELD] Enhanced error handling initialized
2025-07-13 16:43:05 [INFO] atlas_orchestrator: AtlasOrchestrator created - components will load on demand
2025-07-13 16:43:05 [INFO] atlas_orchestrator: Starting comprehensive system initialization...
2025-07-13 16:43:05 [INFO] atlas_database_manager: Database manager created - main: atlas.db, enhanced: 6 databases
2025-07-13 16:43:05 [INFO] atlas_database_manager: Main database schema created: atlas.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Database 'main' initialized: atlas.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Memory database schema created: atlas_memory.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Database 'memory' initialized: atlas_memory.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: RAG database schema created: atlas_rag.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Database 'rag' initialized: atlas_rag.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Compliance database schema created: atlas_compliance.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Database 'compliance' initialized: atlas_compliance.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Feedback database schema created: atlas_feedback.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Database 'feedback' initialized: atlas_feedback.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Enhanced memory database schema created: atlas_enhanced_memory.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: Database 'enhanced_memory' initialized: atlas_enhanced_memory.db
2025-07-13 16:43:05 [INFO] atlas_database_manager: All 6 databases initialized successfully
2025-07-13 16:43:05 [INFO] atlas_orchestrator: Database manager initialized
2025-07-13 16:43:05 [INFO] atlas_ai_engine: [INIT] atlas_ai_engine component logging initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: Predicto AI Engine created - Stock analysis expertise ready
2025-07-13 16:43:10 [INFO] atlas_ai_engine: [AI] Initializing Predicto core components...
2025-07-13 16:43:10 [INFO] atlas_ultimate_100_percent_enforcer: ATLASUltimate100PercentEnforcer initialized - Institutional standards active
2025-07-13 16:43:10 [INFO] atlas_system: [AI] Predicto Engine created - Stock Market Guru mode ready
2025-07-13 16:43:10 [INFO] atlas_system: [LINK] Predicto OpenAI client connected
2025-07-13 16:43:10 [INFO] atlas_system: [OK] Predicto OpenAI client initialized
2025-07-13 16:43:10 [INFO] atlas_system: [AI] Predicto Engine fully initialized - Ready for stock analysis conversations
2025-07-13 16:43:10 [INFO] atlas_ai_engine: [OK] Predicto Conversational Engine initialized
2025-07-13 16:43:10 [INFO] atlas_stock_intelligence_hub: [BRAIN] Stock Intelligence Hub created
2025-07-13 16:43:10 [INFO] atlas_stock_intelligence_hub.TechnicalAnalysis: [OK] Technical Analysis Module initialized
2025-07-13 16:43:10 [INFO] atlas_stock_intelligence_hub.SentimentAnalysis: [OK] Sentiment Analysis Module initialized
2025-07-13 16:43:10 [INFO] atlas_stock_intelligence_hub.PredictionEngine: [OK] Prediction Engine Module initialized
2025-07-13 16:43:10 [INFO] atlas_stock_intelligence_hub.MarketIntelligence: [OK] Market Intelligence Module initialized
2025-07-13 16:43:10 [INFO] atlas_stock_intelligence_hub: [OK] Stock Intelligence Hub fully initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: [OK] Stock Intelligence Hub initialized
2025-07-13 16:43:10 [INFO] atlas_unified_access_layer: [WEB] Unified System Access Layer created
2025-07-13 16:43:10 [INFO] atlas_unified_access_layer: [WEB] Initializing access to 25 A.T.L.A.S features
2025-07-13 16:43:10 [INFO] atlas_unified_access_layer: [OK] Unified System Access Layer fully initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: [OK] Unified System Access Layer initialized
2025-07-13 16:43:10 [INFO] atlas_conversation_flow_manager: [CHAT] Conversation Flow Manager created
2025-07-13 16:43:10 [INFO] atlas_conversation_flow_manager.SuggestionEngine: [OK] Conversation Suggestion Engine initialized
2025-07-13 16:43:10 [INFO] atlas_conversation_flow_manager: [OK] Conversation Flow Manager fully initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: [OK] Conversation Flow Manager initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: [AI] Predicto core components fully initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: Predicto core components initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: OpenAI client connected
2025-07-13 16:43:10 [INFO] atlas_ai_engine: OpenAI client initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: Technical Analysis Agent initialized with ML enhancement
2025-07-13 16:43:10 [INFO] atlas_ai_engine: Risk Management Agent initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: Sentiment Analysis Agent initialized with basic sentiment analysis
2025-07-13 16:43:10 [INFO] atlas_ai_engine: Execution Agent initialized
2025-07-13 16:43:10 [INFO] atlas_ai_engine: [OK] Initialized 4 AI agents with ML capabilities
2025-07-13 16:43:10 [INFO] atlas_ai_engine: Initializing enhanced AI components...
2025-07-13 16:43:10 [INFO] atlas_performance_optimizer: [LAUNCH] Performance Optimizer initialized
2025-07-13 16:43:10 [INFO] atlas_sentiment_analyzer: [THEATER] Sentiment Analyzer initialized - ML: True
2025-07-13 16:43:10 [INFO] atlas_ml_predictor: [AI] LSTM Predictor initialized - ML: False
2025-07-13 16:43:10 [INFO] atlas_sentiment_analyzer: [TOOL] Loading DistilBERT sentiment model...
2025-07-13 16:43:11 [INFO] atlas_sentiment_analyzer: [OK] DistilBERT sentiment model loaded successfully
2025-07-13 16:43:11 [INFO] atlas_ml_predictor: [AI] LSTM predictor running in fallback mode (no ML models)
2025-07-13 16:43:11 [INFO] atlas_ai_engine: ML components initialized
2025-07-13 16:43:11 [INFO] atlas_options_engine: [TARGET] Options Engine initialized - enabled: True
2025-07-13 16:43:11 [INFO] atlas_options_flow_analyzer: [DATA] Options Flow Analyzer initialized - enabled: True, ML: False
2025-07-13 16:43:11 [INFO] atlas_ai_engine: Options components initialized
2025-07-13 16:43:11 [INFO] atlas_portfolio_optimizer: [UP] Portfolio Optimizer initialized - enabled: True
2025-07-13 16:43:11 [INFO] atlas_ai_engine: Portfolio optimizer initialized
2025-07-13 16:43:11 [INFO] atlas_market_context: [WEB] Market Context Engine initialized - enabled: True
2025-07-13 16:43:11 [INFO] atlas_ai_engine: Market context engine initialized
2025-07-13 16:43:11 [INFO] atlas_proactive_assistant: [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 16:43:11 [INFO] atlas_ai_engine: Proactive assistant initialized
2025-07-13 16:43:11 [INFO] atlas_ai_engine: Enhanced AI components initialization complete
2025-07-13 16:43:11 [INFO] atlas_ai_engine: Predicto AI Engine fully initialized - Stock analysis expertise ready
2025-07-13 16:43:11 [INFO] atlas_orchestrator: AI engine initialized
2025-07-13 16:43:11 [INFO] atlas_market_engine: [INIT] atlas_market_engine component logging initialized
2025-07-13 16:43:11 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite initialized with 20+ scanners
2025-07-13 16:43:11 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite available
2025-07-13 16:43:11 [INFO] atlas_market_engine: [SEARCH] Web search configured - Primary: None
2025-07-13 16:43:11 [INFO] atlas_market_engine: [DATA] Market Engine created - API clients will load on demand
2025-07-13 16:43:11 [INFO] atlas_market_engine: [OK] FMP session initialized
2025-07-13 16:43:11 [INFO] atlas_trading_engine: [INIT] atlas_trading_engine component logging initialized
2025-07-13 16:43:11 [INFO] atlas_trading_engine: [TRADE] Trading Engine created - Alpaca client will load on demand
2025-07-13 16:43:11 [INFO] atlas_trading_engine: [OK] Paper trading mode enabled
2025-07-13 16:43:11 [INFO] atlas_trading_engine: [DATA] Loading paper trading positions from database
2025-07-13 16:43:11 [INFO] atlas_trading_engine: [OK] Trading Engine initialization completed
2025-07-13 16:43:11 [INFO] atlas_orchestrator: Trading engine initialized
2025-07-13 16:43:11 [INFO] atlas_risk_engine: [INIT] atlas_risk_engine component logging initialized
2025-07-13 16:43:11 [INFO] atlas_risk_engine: [SHIELD] Risk Engine created - safety guardrails active
2025-07-13 16:43:11 [INFO] atlas_risk_engine: [DATA] Risk parameters loaded - Max position size: 2.0%
2025-07-13 16:43:11 [INFO] atlas_risk_engine: [SEARCH] Risk monitoring systems initialized
2025-07-13 16:43:11 [INFO] atlas_risk_engine: [OK] Risk Engine initialization completed
2025-07-13 16:43:11 [INFO] atlas_orchestrator: Risk engine initialized
2025-07-13 16:43:11 [INFO] atlas_education_engine: [INIT] atlas_education_engine component logging initialized
2025-07-13 16:43:11 [INFO] atlas_education_engine: [LIBRARY] Education Engine created - ChromaDB will load on demand
2025-07-13 16:43:11 [INFO] atlas_education_engine: [BOOK] Basic educational content loaded
2025-07-13 16:43:11 [INFO] atlas_education_engine: [OK] Education Engine initialization completed
2025-07-13 16:43:11 [INFO] atlas_orchestrator: Education engine initialized
2025-07-13 16:43:11 [INFO] atlas_ai_engine: DistilBERT sentiment model loaded
2025-07-13 16:43:12 [INFO] atlas_market_engine: [OK] FMP API connection tested successfully
2025-07-13 16:43:12 [INFO] atlas_market_engine: [INFO] Predicto API configured with placeholder URL - skipping connection test
2025-07-13 16:43:12 [INFO] atlas_enhanced_scanner_suite: [OK] Enhanced Scanner Suite connected to market engine
2025-07-13 16:43:12 [INFO] atlas_market_engine: [OK] Enhanced Scanner Suite integrated
2025-07-13 16:43:12 [INFO] atlas_market_engine: [OK] Market Engine initialization completed
2025-07-13 16:43:12 [INFO] atlas_orchestrator: Market engine initialized
2025-07-13 16:43:12 [INFO] atlas_orchestrator: System initialization completed
2025-07-13 16:43:12 [INFO] atlas_server: A.T.L.A.S system initialization completed successfully
2025-07-13 16:43:47 [INFO] atlas_server: Predicto processing: Hello, I'm testing the web interface...
2025-07-13 16:43:47 [INFO] atlas_orchestrator: Trading God Engine initialized
2025-07-13 16:43:47 [INFO] atlas_ai_engine: [AI] Processing message through Predicto: Hello, I'm testing the web interface...
2025-07-13 16:43:47 [INFO] atlas_system: [TALK] Detected conversational message: 'Hello, I'm testing the web interface...' - using conversational response
2025-07-13 16:43:47 [INFO] atlas_system: [TARGET] Determined persona: conversational - routing to appropriate handler
2025-07-13 16:43:47 [INFO] atlas_system: ? Using conversational mode - natural responses
2025-07-13 16:43:47 [INFO] atlas_system: ? Switched to Conversational persona
2025-07-13 16:43:49 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-13 16:43:49 [INFO] atlas_orchestrator: [SHIELD] Protecting conversational response type 'greeting' from Trading God transformation
