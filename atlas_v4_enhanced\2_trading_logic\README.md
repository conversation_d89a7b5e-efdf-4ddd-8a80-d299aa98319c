# 2. Trading Logic

This folder contains all trading algorithms, pattern detection, and strategy execution components.

## Files:
- `atlas_trading_engine.py` - Core trading execution engine
- `atlas_trading_god_engine.py` - Stock Market God response formatter
- `atlas_advanced_strategies.py` - Advanced trading strategies
- `atlas_ttm_pattern_detector.py` - TTM Squeeze pattern detection
- `atlas_options_engine.py` - Options trading strategies and Greeks
- `atlas_portfolio_optimizer.py` - Portfolio optimization algorithms
- `atlas_risk_engine.py` - Risk management and VaR calculations
- `atlas_goal_based_strategy_generator.py` - Goal-based trading strategies
- `atlas_beginner_trading_mentor.py` - Educational trading mentor

## Purpose:
These files implement all trading logic, from pattern recognition to strategy execution, risk management, and portfolio optimization. This is where the actual "trading brain" of A.T.L.A.S. lives.
