"""
A.T.L.A<PERSON>S Lee Method - Consolidated Pattern Detection System
Combines Lee Method Scanner, Real-time Scanner, and API endpoints
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import pandas as pd
import numpy as np
import requests

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))
try:
    from config import settings
except ImportError:
    # Fallback if config not available
    class MockSettings:
        FMP_API_KEY = "demo"
    settings = MockSettings()

logger = logging.getLogger(__name__)


# ============================================================================
# LEE METHOD DATA STRUCTURES
# ============================================================================

@dataclass
class LeeMethodSignal:
    """Lee Method signal detection result"""
    symbol: str
    signal_type: str  # 'bullish_momentum', 'bearish_momentum', 'neutral'
    entry_price: float
    target_price: float
    stop_loss: float
    confidence: float
    timeframe: str
    timestamp: datetime
    
    # Lee Method specific data
    histogram_sequence: List[float]  # The decreasing + increasing sequence
    momentum_bars: List[float]  # Momentum values for confirmation
    momentum_confirmation: bool
    
    # Multi-timeframe analysis
    weekly_trend: str  # 'bullish', 'bearish', 'neutral'
    daily_trend: str
    trend_alignment: bool  # Weekly and daily trend confirmation
    
    # Risk metrics
    risk_reward_ratio: float
    position_size_percent: float

    def to_dict(self) -> Dict[str, Any]:
        """Convert signal to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'confidence': self.confidence,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'histogram_sequence': self.histogram_sequence,
            'momentum_bars': self.momentum_bars,
            'momentum_confirmation': self.momentum_confirmation,
            'weekly_trend': self.weekly_trend,
            'daily_trend': self.daily_trend,
            'trend_alignment': self.trend_alignment,
            'risk_reward_ratio': self.risk_reward_ratio,
            'position_size_percent': self.position_size_percent
        }


# ============================================================================
# LEE METHOD SCANNER
# ============================================================================

class LeeMethodScanner:
    """Lee Method pattern scanner implementing the 3-criteria algorithm"""
    
    def __init__(self, fmp_api_key: str = None):
        self.logger = logger
        self.fmp_api_key = fmp_api_key or settings.FMP_API_KEY or "demo"
        self.base_url = "https://financialmodelingprep.com/api/v3"
        
        # Lee Method parameters
        self.momentum_period = 12
        self.min_decreasing_bars = 3  # Minimum 3 decreasing bars
        self.max_lookback_bars = 10   # Maximum bars to look back for pattern
        
        # Multi-timeframe parameters
        self.ema_periods = [5, 8, 21, 50]
        
        # Risk management
        self.default_risk_percent = 2.0  # 2% risk per trade
        self.default_reward_ratio = 2.0  # 2:1 reward:risk ratio

    async def fetch_historical_data(self, symbol: str, timeframe: str = "1day", limit: int = 100) -> pd.DataFrame:
        """Fetch historical data from FMP API"""
        try:
            url = f"{self.base_url}/historical-price-full/{symbol}"
            params = {
                "apikey": self.fmp_api_key,
                "timeseries": limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'historical' not in data:
                self.logger.warning(f"No historical data found for {symbol}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(data['historical'])
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            
            # Ensure we have required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    self.logger.error(f"Missing required column: {col}")
                    return pd.DataFrame()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    def calculate_lee_method_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate Lee Method indicators (MACD-based momentum and histogram)"""
        try:
            if len(df) < 26:  # Need enough data for MACD calculation
                return df
            
            # Calculate MACD components
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line
            
            # Add to dataframe
            df['macd'] = macd_line
            df['signal'] = signal_line
            df['histogram'] = histogram
            df['momentum'] = histogram.rolling(window=self.momentum_period).mean()
            
            # Calculate EMAs for trend analysis
            for period in self.ema_periods:
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error calculating Lee Method indicators: {e}")
            return df

    def detect_lee_method_pattern(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Detect Lee Method pattern based on the 3 criteria:
        1. Three (or more) histogram bars that decrease, followed by an increase
        2. Momentum should be greater than the prior momentum bar
        3. Multi-timeframe trend confirmation (weekly/daily)
        """
        try:
            if len(df) < self.max_lookback_bars:
                return None
            
            # Get recent data for pattern analysis
            recent_data = df.tail(self.max_lookback_bars)
            histogram_values = recent_data['histogram'].values
            momentum_values = recent_data['momentum'].values
            
            # Criterion 1: Find decreasing sequence followed by increase
            pattern_result = self._find_decreasing_increasing_pattern(histogram_values)
            if not pattern_result:
                return None
            
            # Criterion 2: Momentum confirmation
            momentum_confirmed = self._check_momentum_confirmation(
                momentum_values, pattern_result['increase_index']
            )
            if not momentum_confirmed:
                return None
            
            # Criterion 3: Multi-timeframe trend analysis
            trend_analysis = self._analyze_multi_timeframe_trends(df)
            
            # Determine signal direction and strength
            signal_direction = self._determine_signal_direction(
                pattern_result, momentum_values, trend_analysis
            )
            
            return {
                'pattern_found': True,
                'histogram_sequence': pattern_result['sequence'],
                'momentum_confirmation': momentum_confirmed,
                'trend_analysis': trend_analysis,
                'signal_direction': signal_direction,
                'confidence': self._calculate_confidence(pattern_result, trend_analysis),
                'entry_index': len(recent_data) - 1  # Current bar
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting Lee Method pattern: {e}")
            return None

    def _find_decreasing_increasing_pattern(self, histogram_values: np.ndarray) -> Optional[Dict[str, Any]]:
        """Find decreasing sequence followed by increase in histogram"""
        try:
            # Look for pattern in recent bars
            for i in range(len(histogram_values) - self.min_decreasing_bars - 1, -1, -1):
                # Check for decreasing sequence
                decreasing_count = 0
                for j in range(i, len(histogram_values) - 1):
                    if histogram_values[j] > histogram_values[j + 1]:
                        decreasing_count += 1
                    else:
                        break
                
                # Check if we have enough decreasing bars followed by increase
                if decreasing_count >= self.min_decreasing_bars:
                    increase_index = i + decreasing_count
                    if increase_index < len(histogram_values) - 1:
                        if histogram_values[increase_index + 1] > histogram_values[increase_index]:
                            return {
                                'start_index': i,
                                'decrease_count': decreasing_count,
                                'increase_index': increase_index + 1,
                                'sequence': histogram_values[i:increase_index + 2].tolist()
                            }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding decreasing-increasing pattern: {e}")
            return None

    def _check_momentum_confirmation(self, momentum_values: np.ndarray, increase_index: int) -> bool:
        """Check if momentum is greater than prior momentum bar"""
        try:
            if increase_index < 1 or increase_index >= len(momentum_values):
                return False
            
            current_momentum = momentum_values[increase_index]
            prior_momentum = momentum_values[increase_index - 1]
            
            return current_momentum > prior_momentum
            
        except Exception as e:
            self.logger.error(f"Error checking momentum confirmation: {e}")
            return False

    def _analyze_multi_timeframe_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze multi-timeframe trends for confirmation"""
        try:
            if len(df) < 50:  # Need enough data for trend analysis
                return {
                    'daily_trend': 'neutral',
                    'weekly_trend': 'neutral',
                    'trend_alignment': False,
                    'ema_alignment': False
                }
            
            current = df.iloc[-1]
            
            # Daily trend (based on EMA alignment)
            daily_trend = 'bullish' if current['ema_5'] > current['ema_21'] else 'bearish'
            
            # Weekly trend (simplified - based on longer EMAs)
            weekly_trend = 'bullish' if current['ema_21'] > current['ema_50'] else 'bearish'
            
            # Trend alignment
            trend_alignment = (
                (daily_trend == 'bullish' and weekly_trend == 'bullish') or
                (daily_trend == 'bearish' and weekly_trend == 'bearish')
            )
            
            return {
                'daily_trend': daily_trend,
                'weekly_trend': weekly_trend,
                'trend_alignment': trend_alignment,
                'ema_alignment': current['ema_5'] > current['ema_8'] > current['ema_21']
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing multi-timeframe trends: {e}")
            return {
                'daily_trend': 'neutral',
                'weekly_trend': 'neutral',
                'trend_alignment': False,
                'ema_alignment': False
            }

    def _determine_signal_direction(self, pattern_result: Dict[str, Any], 
                                  momentum_values: np.ndarray, 
                                  trend_analysis: Dict[str, Any]) -> str:
        """Determine signal direction based on pattern and trends"""
        try:
            # Check if histogram is moving from negative to positive (bullish)
            histogram_sequence = pattern_result['sequence']
            
            if len(histogram_sequence) >= 2:
                if histogram_sequence[-1] > histogram_sequence[-2]:
                    if trend_analysis['trend_alignment'] and trend_analysis['daily_trend'] == 'bullish':
                        return 'bullish_momentum'
                    elif histogram_sequence[-1] > 0:
                        return 'bullish_momentum'
                    else:
                        return 'bearish_momentum'
            
            return 'neutral'
            
        except Exception as e:
            self.logger.error(f"Error determining signal direction: {e}")
            return 'neutral'

    def _calculate_confidence(self, pattern_result: Dict[str, Any], 
                            trend_analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for the signal"""
        try:
            confidence = 0.5  # Base confidence
            
            # Add confidence for strong pattern
            if pattern_result['decrease_count'] >= 4:
                confidence += 0.2
            
            # Add confidence for trend alignment
            if trend_analysis['trend_alignment']:
                confidence += 0.2
            
            # Add confidence for EMA alignment
            if trend_analysis['ema_alignment']:
                confidence += 0.1
            
            return min(1.0, confidence)
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return 0.5

    async def scan_symbol(self, symbol: str) -> Optional[LeeMethodSignal]:
        """Scan a single symbol for Lee Method patterns"""
        try:
            # Fetch historical data
            df = await self.fetch_historical_data(symbol, limit=100)
            if df.empty:
                return None
            
            # Calculate Lee Method indicators
            df_with_indicators = self.calculate_lee_method_indicators(df)
            
            # Detect pattern
            pattern_result = self.detect_lee_method_pattern(df_with_indicators)
            if not pattern_result or not pattern_result['pattern_found']:
                return None
            
            # Calculate entry, target, and stop prices
            current_price = df_with_indicators['close'].iloc[-1]
            entry_price = current_price
            
            # Calculate target and stop based on signal direction
            if pattern_result['signal_direction'] == 'bullish_momentum':
                target_price = entry_price * (1 + self.default_reward_ratio * self.default_risk_percent / 100)
                stop_loss = entry_price * (1 - self.default_risk_percent / 100)
            else:
                target_price = entry_price * (1 - self.default_reward_ratio * self.default_risk_percent / 100)
                stop_loss = entry_price * (1 + self.default_risk_percent / 100)
            
            # Calculate risk/reward ratio
            risk_reward_ratio = abs(target_price - entry_price) / abs(entry_price - stop_loss)
            
            # Create Lee Method signal
            signal = LeeMethodSignal(
                symbol=symbol,
                signal_type=pattern_result['signal_direction'],
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                confidence=pattern_result['confidence'],
                timeframe='daily',
                timestamp=datetime.now(),
                histogram_sequence=pattern_result['histogram_sequence'],
                momentum_bars=df_with_indicators['momentum'].tail(5).tolist(),
                momentum_confirmation=pattern_result['momentum_confirmation'],
                weekly_trend=pattern_result['trend_analysis']['weekly_trend'],
                daily_trend=pattern_result['trend_analysis']['daily_trend'],
                trend_alignment=pattern_result['trend_analysis']['trend_alignment'],
                risk_reward_ratio=risk_reward_ratio,
                position_size_percent=self.default_risk_percent
            )

            return signal
            
        except Exception as e:
            self.logger.error(f"Error scanning symbol {symbol}: {e}")
            return None

    async def scan_multiple_symbols(self, symbols: List[str]) -> List[LeeMethodSignal]:
        """Scan multiple symbols for Lee Method patterns"""
        signals = []

        try:
            # Process symbols in batches to avoid overwhelming APIs
            batch_size = 5
            for i in range(0, len(symbols), batch_size):
                batch = symbols[i:i + batch_size]

                # Create tasks for concurrent processing
                tasks = [self.scan_symbol(symbol) for symbol in batch]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process results
                for result in batch_results:
                    if isinstance(result, LeeMethodSignal):
                        signals.append(result)
                    elif isinstance(result, Exception):
                        self.logger.error(f"Error in batch processing: {result}")

                # Small delay between batches
                await asyncio.sleep(0.5)

            return signals

        except Exception as e:
            self.logger.error(f"Error scanning multiple symbols: {e}")
            return signals


# ============================================================================
# REAL-TIME SCANNER
# ============================================================================

class AtlasLeeMethodRealtimeScanner:
    """Real-time Lee Method scanner for A.T.L.A.S. system"""
    
    def __init__(self, fmp_api_key: str = None):
        self.logger = logger
        self.lee_scanner = LeeMethodScanner(fmp_api_key)
        
        # Scanner configuration
        self.scan_symbols = [
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
            "AMD", "INTC", "CRM", "ORCL", "ADBE", "PYPL", "UBER", "LYFT",
            "SHOP", "SQ", "ROKU", "ZM", "DOCU", "SNOW", "PLTR", "COIN"
        ]
        
        # Scanner state
        self.is_running = False
        self.scan_interval = 30  # seconds
        self.last_scan_time = None
        self.scan_count = 0
        self.active_signals = {}
        self.scan_task = None
        
        # Performance tracking
        self.scan_times = []
        self.error_count = 0

    async def start_scanning(self):
        """Start the real-time scanning process"""
        if self.is_running:
            self.logger.warning("Scanner is already running")
            return
        
        self.is_running = True
        self.logger.info("[SCANNER] Starting Lee Method real-time scanner")
        
        # Start scanning task
        self.scan_task = asyncio.create_task(self._scanning_loop())

    async def stop_scanning(self):
        """Stop the real-time scanning process"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.scan_task:
            self.scan_task.cancel()
            try:
                await self.scan_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("[SCANNER] Lee Method scanner stopped")

    async def _scanning_loop(self):
        """Main scanning loop"""
        while self.is_running:
            try:
                await self._perform_lee_method_scan()
                await asyncio.sleep(self.scan_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in scanning loop: {e}")
                self.error_count += 1
                await asyncio.sleep(5)  # Brief pause on error

    async def _perform_lee_method_scan(self):
        """Perform Lee Method scan on all symbols"""
        try:
            scan_start = datetime.now()
            self.scan_count += 1
            self.last_scan_time = scan_start
            
            # Clear old signals (older than 1 hour)
            self._cleanup_old_signals()
            
            # Scan symbols in batches to avoid overwhelming APIs
            batch_size = 5
            new_signals = []
            
            for i in range(0, len(self.scan_symbols), batch_size):
                batch = self.scan_symbols[i:i + batch_size]
                batch_results = await self._scan_batch(batch)
                new_signals.extend(batch_results)
                
                # Small delay between batches
                await asyncio.sleep(0.5)
            
            # Update active signals
            for signal in new_signals:
                self.active_signals[signal.symbol] = signal
            
            # Track performance
            scan_duration = (datetime.now() - scan_start).total_seconds()
            self.scan_times.append(scan_duration)
            
            # Keep only recent scan times (last 100)
            if len(self.scan_times) > 100:
                self.scan_times = self.scan_times[-100:]
            
            self.logger.info(f"[SCANNER] Scan #{self.scan_count} completed in {scan_duration:.2f}s - {len(new_signals)} new signals")
            
        except Exception as e:
            self.logger.error(f"Error performing Lee Method scan: {e}")
            self.error_count += 1

    async def _scan_batch(self, symbols: List[str]) -> List[LeeMethodSignal]:
        """Scan a batch of symbols"""
        signals = []
        
        try:
            tasks = [self.lee_scanner.scan_symbol(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in results:
                if isinstance(result, LeeMethodSignal):
                    signals.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Error in batch scan: {result}")
            
        except Exception as e:
            self.logger.error(f"Error scanning batch {symbols}: {e}")
        
        return signals

    def _cleanup_old_signals(self):
        """Remove signals older than 1 hour"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        symbols_to_remove = []
        for symbol, signal in self.active_signals.items():
            if signal.timestamp < cutoff_time:
                symbols_to_remove.append(symbol)
        
        for symbol in symbols_to_remove:
            del self.active_signals[symbol]

    def get_latest_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the latest Lee Method signals"""
        signals = list(self.active_signals.values())
        signals.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [signal.to_dict() for signal in signals[:limit]]

    def get_scanner_status(self) -> Dict[str, Any]:
        """Get scanner status information"""
        avg_scan_time = sum(self.scan_times) / len(self.scan_times) if self.scan_times else 0
        
        return {
            'is_running': self.is_running,
            'scan_count': self.scan_count,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'active_signals_count': len(self.active_signals),
            'symbols_monitored': len(self.scan_symbols),
            'error_count': self.error_count,
            'average_scan_time': round(avg_scan_time, 2),
            'scan_interval': self.scan_interval
        }


# ============================================================================
# SCANNER INSTANCE MANAGEMENT
# ============================================================================

_scanner_instance = None

def get_scanner_instance(fmp_api_key: str = None) -> AtlasLeeMethodRealtimeScanner:
    """Get or create scanner instance"""
    global _scanner_instance
    
    if _scanner_instance is None:
        _scanner_instance = AtlasLeeMethodRealtimeScanner(fmp_api_key)
    
    return _scanner_instance


# ============================================================================
# LEE METHOD CRITERIA INFORMATION
# ============================================================================

def get_lee_method_criteria() -> Dict[str, Any]:
    """Get Lee Method criteria information"""
    return {
        'name': 'Lee Method',
        'description': 'Advanced momentum pattern detection replacing TTM Squeeze',
        'criteria': [
            {
                'number': 1,
                'description': 'Three (or more) histogram bars that decrease, followed by an increase',
                'detail': 'Pattern must show at least 3 consecutive decreasing momentum bars followed by an uptick'
            },
            {
                'number': 2,
                'description': 'Momentum should be greater than the prior momentum bar',
                'detail': 'The momentum increase must be confirmed by higher momentum than previous bar'
            },
            {
                'number': 3,
                'description': 'Identify significant shifts from weekly and daily charts',
                'detail': 'Multi-timeframe analysis confirming weekly trend with daily trend alignment'
            }
        ],
        'advantages': [
            'More precise entry timing than TTM Squeeze',
            'Better momentum confirmation',
            'Multi-timeframe trend validation',
            'Reduced false signals'
        ]
    }


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "LeeMethodScanner",
    "AtlasLeeMethodRealtimeScanner",
    "LeeMethodSignal",
    "get_scanner_instance",
    "get_lee_method_criteria"
]
