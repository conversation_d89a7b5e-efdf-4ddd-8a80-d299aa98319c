2025-07-13 16:25:24 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:25:24 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:25:24 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:25:24 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:25:24 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 16:33:01 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:33:01 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:33:01 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:33:01 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:33:01 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 16:34:23 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:34:23 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:34:23 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:34:23 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:34:23 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 16:43:05 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:43:05 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:43:05 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:43:05 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:43:05 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 17:02:08 [INFO] atlas_error_handler:94 - __init__(): [SHIELD] Enhanced Error Handler initialized
2025-07-13 17:02:08 [INFO] atlas_performance_optimizer:153 - __init__(): [LAUNCH] Performance Optimizer initialized
2025-07-13 17:02:08 [INFO] atlas_security:249 - __init__(): [SHIELD] Security Manager initialized
2025-07-13 17:02:08 [INFO] atlas_proactive_assistant:308 - __init__(): [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 17:02:08 [INFO] atlas_logging:54 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 17:02:08 [INFO] atlas_logging:55 - setup_atlas_logging(): Log level: INFO
2025-07-13 17:02:08 [INFO] atlas_logging:56 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 17:02:08 [INFO] __main__:79 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 17:02:08 [INFO] __main__:80 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 17:02:08 [INFO] __main__:81 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 17:02:08 [INFO] __main__:126 - initialize_system(): Starting background initialization...
2025-07-13 17:02:08 [INFO] __main__:138 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 17:02:08 [INFO] atlas_orchestrator:74 - initialize_component_logging(): [INIT] atlas_orchestrator component logging initialized
2025-07-13 17:02:08 [INFO] __main__:142 - initialize_system(): Initializing AtlasOrchestrator...
2025-07-13 17:02:08 [WARNING] atlas_orchestrator:109 - __init__(): [WARN] Error handler setup failed: name 'setup_component_recovery_strategies' is not defined
2025-07-13 17:02:08 [INFO] atlas_orchestrator:114 - __init__(): AtlasOrchestrator created - components will load on demand
2025-07-13 17:02:08 [INFO] atlas_orchestrator:131 - initialize_with_progress(): Starting comprehensive system initialization...
2025-07-13 17:02:08 [INFO] atlas_security:249 - __init__(): [SHIELD] Security Manager initialized
2025-07-13 17:02:08 [ERROR] atlas_orchestrator:193 - _ensure_database_manager(): Database manager initialization failed: 'AtlasSecurityManager' object has no attribute 'initialize'
2025-07-13 17:02:08 [ERROR] atlas_orchestrator:178 - initialize_with_progress(): System initialization failed: 'AtlasSecurityManager' object has no attribute 'initialize'
2025-07-13 17:02:08 [ERROR] __main__:159 - initialize_system(): System initialization failed: 'AtlasSecurityManager' object has no attribute 'initialize'
2025-07-13 17:06:42 [INFO] atlas_error_handler:94 - __init__(): [SHIELD] Enhanced Error Handler initialized
2025-07-13 17:06:42 [INFO] atlas_performance_optimizer:153 - __init__(): [LAUNCH] Performance Optimizer initialized
2025-07-13 17:06:42 [INFO] atlas_security:249 - __init__(): [SHIELD] Security Manager initialized
2025-07-13 17:06:42 [INFO] atlas_proactive_assistant:308 - __init__(): [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 17:06:42 [INFO] atlas_logging:54 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 17:06:42 [INFO] atlas_logging:55 - setup_atlas_logging(): Log level: INFO
2025-07-13 17:06:42 [INFO] atlas_logging:56 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 17:06:42 [INFO] __main__:79 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 17:06:42 [INFO] __main__:80 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 17:06:42 [INFO] __main__:81 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 17:06:42 [INFO] __main__:126 - initialize_system(): Starting background initialization...
2025-07-13 17:06:42 [INFO] __main__:138 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 17:06:42 [INFO] atlas_orchestrator:74 - initialize_component_logging(): [INIT] atlas_orchestrator component logging initialized
2025-07-13 17:06:42 [INFO] __main__:142 - initialize_system(): Initializing AtlasOrchestrator...
2025-07-13 17:06:42 [WARNING] atlas_orchestrator:109 - __init__(): [WARN] Error handler setup failed: name 'setup_component_recovery_strategies' is not defined
2025-07-13 17:06:42 [INFO] atlas_orchestrator:114 - __init__(): AtlasOrchestrator created - components will load on demand
2025-07-13 17:06:42 [INFO] atlas_orchestrator:131 - initialize_with_progress(): Starting comprehensive system initialization...
2025-07-13 17:06:42 [INFO] atlas_database:42 - __init__(): Database manager created - main: atlas.db, enhanced: 6 databases
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Main database schema created: atlas.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'main' initialized: atlas.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Memory database schema created: atlas_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'memory' initialized: atlas_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Rag database schema created: atlas_rag.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'rag' initialized: atlas_rag.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Compliance database schema created: atlas_compliance.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'compliance' initialized: atlas_compliance.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Feedback database schema created: atlas_feedback.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'feedback' initialized: atlas_feedback.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Enhanced_Memory database schema created: atlas_enhanced_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'enhanced_memory' initialized: atlas_enhanced_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:58 - initialize(): All 6 databases initialized successfully
2025-07-13 17:06:42 [INFO] atlas_orchestrator:191 - _ensure_database_manager(): Database manager initialized
