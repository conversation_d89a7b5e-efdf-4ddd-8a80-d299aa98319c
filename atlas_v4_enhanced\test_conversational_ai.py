#!/usr/bin/env python3
"""
A.T.L.A.S. Conversational AI Testing Suite
Tests beginner-level trading questions across multiple categories
"""

import requests
import json
import time
from datetime import datetime

class ATLASConversationalTester:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session_id = f"test_session_{int(time.time())}"
        self.test_results = []
        
    def test_chat(self, question, category, expected_elements=None):
        """Test a single chat question and evaluate the response"""
        print(f"\n{'='*60}")
        print(f"CATEGORY: {category}")
        print(f"QUESTION: {question}")
        print(f"{'='*60}")
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/chat",
                json={
                    "message": question,
                    "session_id": self.session_id
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get("response", "No response")
                
                print(f"RESPONSE:\n{ai_response}")
                
                # Evaluate response quality
                evaluation = self.evaluate_response(ai_response, category, expected_elements)
                
                self.test_results.append({
                    "question": question,
                    "category": category,
                    "response": ai_response,
                    "evaluation": evaluation,
                    "timestamp": datetime.now().isoformat()
                })
                
                return True
                
            else:
                print(f"ERROR: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"ERROR: {e}")
            return False
    
    def evaluate_response(self, response, category, expected_elements):
        """Evaluate response quality based on criteria"""
        evaluation = {
            "has_6_point_format": False,
            "beginner_friendly": False,
            "specific_numbers": False,
            "risk_management": False,
            "confidence_score": False,
            "educational_value": False,
            "overall_score": 0
        }
        
        response_lower = response.lower()
        
        # Check for 6-point format
        if any(marker in response for marker in ["1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣"]):
            evaluation["has_6_point_format"] = True
        
        # Check for beginner-friendly language
        if any(phrase in response_lower for phrase in ["think of it like", "imagine", "simply put", "in plain english", "for beginners"]):
            evaluation["beginner_friendly"] = True
            
        # Check for specific numbers
        if any(char in response for char in ["$", "%"]) and any(word in response_lower for word in ["price", "percent", "dollar"]):
            evaluation["specific_numbers"] = True
            
        # Check for risk management
        if any(term in response_lower for term in ["stop loss", "risk", "protect", "safety", "position size"]):
            evaluation["risk_management"] = True
            
        # Check for confidence score
        if any(term in response_lower for term in ["confidence", "certainty", "probability"]) and "%" in response:
            evaluation["confidence_score"] = True
            
        # Check for educational value
        if any(term in response_lower for term in ["learn", "understand", "explain", "concept", "basics"]):
            evaluation["educational_value"] = True
        
        # Calculate overall score
        score = sum(evaluation.values()) - 1  # Subtract 1 for overall_score itself
        evaluation["overall_score"] = round((score / 6) * 100, 1)
        
        print(f"\nEVALUATION:")
        for key, value in evaluation.items():
            if key != "overall_score":
                status = "✅" if value else "❌"
                print(f"  {status} {key.replace('_', ' ').title()}: {value}")
        print(f"  📊 Overall Score: {evaluation['overall_score']}%")
        
        return evaluation

def run_comprehensive_test():
    """Run comprehensive testing suite"""
    tester = ATLASConversationalTester()
    
    print("🚀 A.T.L.A.S. CONVERSATIONAL AI TESTING SUITE")
    print("=" * 80)
    print("Testing beginner-level trading questions across multiple categories")
    print("=" * 80)
    
    # Wait for server to be ready
    time.sleep(2)
    
    # Test questions by category
    test_questions = [
        # Basic Trading Concepts (5 questions)
        ("What is a stock?", "Basic Trading Concepts"),
        ("How do I start trading?", "Basic Trading Concepts"),
        ("What's the difference between buying and selling?", "Basic Trading Concepts"),
        ("What is the stock market?", "Basic Trading Concepts"),
        ("How do stocks make money?", "Basic Trading Concepts"),
        
        # Stock Analysis Requests (6 questions)
        ("Should I buy AAPL?", "Stock Analysis"),
        ("Analyze Tesla for me", "Stock Analysis"),
        ("Is Microsoft a good investment?", "Stock Analysis"),
        ("What do you think about Amazon stock?", "Stock Analysis"),
        ("Give me your opinion on Google stock", "Stock Analysis"),
        ("Should I invest in Netflix?", "Stock Analysis"),
        
        # Goal-Oriented Trading (5 questions)
        ("I want to make $50 today", "Goal-Oriented Trading"),
        ("Help me invest $1000 safely", "Goal-Oriented Trading"),
        ("I have $500, what should I do?", "Goal-Oriented Trading"),
        ("I want to double my money", "Goal-Oriented Trading"),
        ("How can I make money with stocks?", "Goal-Oriented Trading"),
        
        # Risk Management (5 questions)
        ("How much should I risk?", "Risk Management"),
        ("What's a stop loss?", "Risk Management"),
        ("How do I protect my money?", "Risk Management"),
        ("What if I lose money?", "Risk Management"),
        ("How do I manage risk?", "Risk Management"),
        
        # Lee Method Specific (4 questions)
        ("What is the Lee Method?", "Lee Method"),
        ("Scan for Lee Method patterns", "Lee Method"),
        ("Explain momentum patterns", "Lee Method"),
        ("How does Lee Method work?", "Lee Method"),
        
        # Educational Queries (5 questions)
        ("Teach me about options", "Educational"),
        ("What are the basics of trading?", "Educational"),
        ("How do professionals trade?", "Educational"),
        ("Explain technical analysis", "Educational"),
        ("What should a beginner know?", "Educational")
    ]
    
    successful_tests = 0
    total_tests = len(test_questions)
    
    for question, category in test_questions:
        success = tester.test_chat(question, category)
        if success:
            successful_tests += 1
        time.sleep(1)  # Brief pause between tests
    
    # Generate summary report
    print(f"\n{'='*80}")
    print("📊 TESTING SUMMARY REPORT")
    print(f"{'='*80}")
    print(f"Total Tests: {total_tests}")
    print(f"Successful Tests: {successful_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    # Category analysis
    categories = {}
    for result in tester.test_results:
        cat = result["category"]
        if cat not in categories:
            categories[cat] = {"count": 0, "total_score": 0}
        categories[cat]["count"] += 1
        categories[cat]["total_score"] += result["evaluation"]["overall_score"]
    
    print(f"\n📈 CATEGORY PERFORMANCE:")
    for category, data in categories.items():
        avg_score = data["total_score"] / data["count"]
        print(f"  {category}: {avg_score:.1f}% average ({data['count']} tests)")
    
    return tester.test_results

if __name__ == "__main__":
    results = run_comprehensive_test()
