"""
A.T.L.A.S Orchestrator - Main System Orchestrator
Coordinates all A.T.L.A.S. engines and components
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from config import settings
from models import EngineStatus

# Import all consolidated engines
from atlas_ai_core import AtlasAIEngine
from atlas_trading_core import AtlasTradingEngine
from atlas_market_core import AtlasMarketEngine
from atlas_risk_core import AtlasRiskEngine
from atlas_education import AtlasEducationEngine
from atlas_database import AtlasDatabaseManager
from atlas_lee_method import LeeMethodScanner
from atlas_utils import AtlasUtilsOrchestrator

logger = logging.getLogger(__name__)

# ============================================================================
# MAIN ORCHESTRATOR
# ============================================================================

class AtlasOrchestrator:
    """Main system orchestrator for A.T.L.A.S."""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.engines = {}
        self.initialization_order = [
            'database',
            'utils',
            'market',
            'risk',
            'trading',
            'education',
            'ai',
            'lee_method'
        ]
        
        logger.info("[ORCHESTRATOR] A.T.L.A.S. Orchestrator initialized")

    async def initialize(self):
        """Initialize all A.T.L.A.S. engines in proper order"""
        try:
            self.status = EngineStatus.INITIALIZING
            logger.info("[INIT] Starting A.T.L.A.S. system initialization...")
            
            # Initialize engines in order
            for engine_name in self.initialization_order:
                await self._initialize_engine(engine_name)
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] A.T.L.A.S. system initialization completed successfully")
            
        except Exception as e:
            logger.error(f"A.T.L.A.S. initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_engine(self, engine_name: str):
        """Initialize individual engine"""
        try:
            logger.info(f"[INIT] Initializing {engine_name} engine...")
            
            if engine_name == 'database':
                self.engines['database'] = AtlasDatabaseManager()
                await self.engines['database'].initialize()
                
            elif engine_name == 'utils':
                self.engines['utils'] = AtlasUtilsOrchestrator()
                await self.engines['utils'].initialize()
                
            elif engine_name == 'market':
                self.engines['market'] = AtlasMarketEngine()
                await self.engines['market'].initialize()
                
            elif engine_name == 'risk':
                self.engines['risk'] = AtlasRiskEngine()
                await self.engines['risk'].initialize()
                
            elif engine_name == 'trading':
                self.engines['trading'] = AtlasTradingEngine()
                await self.engines['trading'].initialize()
                
            elif engine_name == 'education':
                self.engines['education'] = AtlasEducationEngine()
                await self.engines['education'].initialize()
                
            elif engine_name == 'ai':
                self.engines['ai'] = AtlasAIEngine()
                await self.engines['ai'].initialize()
                
            elif engine_name == 'lee_method':
                self.engines['lee_method'] = LeeMethodScanner()
                if hasattr(self.engines['lee_method'], 'initialize'):
                    await self.engines['lee_method'].initialize()
            
            logger.info(f"[OK] {engine_name} engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {engine_name} engine: {e}")
            raise

    async def process_message(self, message: str, session_id: str = None, 
                            user_id: str = None) -> Dict[str, Any]:
        """Process user message through AI engine"""
        try:
            if 'ai' not in self.engines:
                return {
                    'response': 'AI engine not available',
                    'type': 'error',
                    'confidence': 0.0,
                    'context': {}
                }
            
            # Process through AI engine
            ai_response = await self.engines['ai'].process_message(
                message=message,
                session_id=session_id,
                user_id=user_id
            )

            # Convert AIResponse to dictionary
            return {
                'response': ai_response.response,
                'type': ai_response.type,
                'confidence': ai_response.confidence,
                'context': ai_response.context,
                'suggestions': getattr(ai_response, 'suggestions', None)
            }
            
        except Exception as e:
            logger.error(f"Message processing failed: {e}")
            return {
                'response': 'I encountered an error processing your message. Please try again.',
                'type': 'error',
                'confidence': 0.0,
                'context': {'error': str(e)}
            }

    async def analyze_stock(self, symbol: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Perform stock analysis"""
        try:
            if 'trading' not in self.engines:
                return {'error': 'Trading engine not available'}
            
            # Use trading engine for analysis
            analysis = await self.engines['trading'].analyze_stock(symbol, analysis_type)
            return analysis
            
        except Exception as e:
            logger.error(f"Stock analysis failed for {symbol}: {e}")
            return {'error': str(e)}

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            engine_statuses = {}
            
            for engine_name, engine in self.engines.items():
                try:
                    if hasattr(engine, 'status'):
                        engine_statuses[engine_name] = engine.status.value
                    else:
                        engine_statuses[engine_name] = 'unknown'
                except:
                    engine_statuses[engine_name] = 'error'
            
            return {
                'timestamp': datetime.now().isoformat(),
                'orchestrator_status': self.status.value,
                'engines': engine_statuses,
                'total_engines': len(self.engines),
                'active_engines': sum(1 for status in engine_statuses.values() if status == 'active'),
                'system_health': 'healthy' if self.status == EngineStatus.ACTIVE else 'unhealthy'
            }
            
        except Exception as e:
            logger.error(f"System status check failed: {e}")
            return {'error': str(e)}

    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        try:
            if 'trading' not in self.engines:
                return {'error': 'Trading engine not available'}
            
            # Get portfolio from trading engine
            portfolio = await self.engines['trading'].get_portfolio_summary()
            return portfolio
            
        except Exception as e:
            logger.error(f"Portfolio summary failed: {e}")
            return {'error': str(e)}

    async def get_educational_content(self, topic: str = None) -> Dict[str, Any]:
        """Get educational content"""
        try:
            if 'education' not in self.engines:
                return {'error': 'Education engine not available'}
            
            # Get content from education engine
            content = await self.engines['education'].get_educational_content(topic)
            return content
            
        except Exception as e:
            logger.error(f"Educational content retrieval failed: {e}")
            return {'error': str(e)}

    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get market data for symbol"""
        try:
            if 'market' not in self.engines:
                return {'error': 'Market engine not available'}
            
            # Get data from market engine
            data = await self.engines['market'].get_market_data(symbol)
            return data
            
        except Exception as e:
            logger.error(f"Market data retrieval failed for {symbol}: {e}")
            return {'error': str(e)}

    async def assess_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess portfolio risk"""
        try:
            if 'risk' not in self.engines:
                return {'error': 'Risk engine not available'}
            
            # Assess risk using risk engine
            risk_assessment = await self.engines['risk'].assess_portfolio_risk(portfolio_data)
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'error': str(e)}

    async def scan_lee_method(self, symbols: List[str] = None) -> Dict[str, Any]:
        """Scan for Lee Method patterns"""
        try:
            if 'lee_method' not in self.engines:
                return {'error': 'Lee Method scanner not available'}
            
            # Scan using Lee Method engine
            results = await self.engines['lee_method'].scan_symbols(symbols)
            return results
            
        except Exception as e:
            logger.error(f"Lee Method scan failed: {e}")
            return {'error': str(e)}

    async def shutdown(self):
        """Shutdown all engines gracefully"""
        try:
            logger.info("[SHUTDOWN] Starting A.T.L.A.S. system shutdown...")
            
            # Shutdown engines in reverse order
            for engine_name in reversed(self.initialization_order):
                if engine_name in self.engines:
                    try:
                        engine = self.engines[engine_name]
                        if hasattr(engine, 'shutdown'):
                            await engine.shutdown()
                        logger.info(f"[SHUTDOWN] {engine_name} engine shutdown completed")
                    except Exception as e:
                        logger.error(f"Error shutting down {engine_name} engine: {e}")
            
            self.status = EngineStatus.STOPPED
            logger.info("[SHUTDOWN] A.T.L.A.S. system shutdown completed")
            
        except Exception as e:
            logger.error(f"System shutdown error: {e}")

    def get_engine(self, engine_name: str):
        """Get specific engine instance"""
        return self.engines.get(engine_name)

    def is_ready(self) -> bool:
        """Check if system is ready"""
        return self.status == EngineStatus.ACTIVE

    def get_engine_count(self) -> int:
        """Get total number of engines"""
        return len(self.engines)

    def get_active_engine_count(self) -> int:
        """Get number of active engines"""
        active_count = 0
        for engine in self.engines.values():
            try:
                if hasattr(engine, 'status') and engine.status == EngineStatus.ACTIVE:
                    active_count += 1
            except:
                pass
        return active_count


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasOrchestrator"]
