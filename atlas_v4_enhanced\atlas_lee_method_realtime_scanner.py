#!/usr/bin/env python3
"""
A.T.L.A.S<PERSON> Lee Method Real-time Scanner
Integrates Lee Method pattern detection with the existing A.T.L.A.S. infrastructure
"""

import asyncio
import logging
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

# Add helper tools to path for config access
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))
try:
    from config import settings
except ImportError:
    # Fallback if config not available
    class MockSettings:
        FMP_API_KEY = "demo"
    settings = MockSettings()

from lee_method_scanner import LeeMethodScanner, LeeMethodSignal

# Configure logging with ASCII-safe format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('atlas_lee_method_realtime.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class AtlasLeeMethodRealtimeScanner:
    """Real-time Lee Method scanner for A.T.L.A.S. system"""
    
    def __init__(self, fmp_api_key: str = None):
        self.logger = logger
        self.lee_scanner = LeeMethodScanner(fmp_api_key)
        
        # Scanner configuration
        self.scan_symbols = [
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
            "AMD", "INTC", "CRM", "ORCL", "ADBE", "PYPL", "UBER", "LYFT",
            "SHOP", "SQ", "ROKU", "ZM", "DOCU", "SNOW", "PLTR", "COIN"
        ]
        
        # Scanner state
        self.is_running = False
        self.scan_interval = 30  # seconds
        self.last_scan_time = None
        self.scan_count = 0
        self.active_signals = {}
        self.scan_task = None
        
        # Performance tracking
        self.scan_times = []
        self.error_count = 0
        
    async def start_scanning(self):
        """Start real-time Lee Method scanning"""
        if self.is_running:
            self.logger.warning("Lee Method scanner already running")
            return
        
        self.is_running = True
        self.logger.info("[LAUNCH] Starting real-time Lee Method scanner...")
        
        try:
            self.scan_task = asyncio.create_task(self._scan_loop())
            await self.scan_task
        except asyncio.CancelledError:
            self.logger.info("Lee Method scanner stopped")
        except Exception as e:
            self.logger.error(f"Lee Method scanner error: {e}")
        finally:
            self.is_running = False
    
    def stop_scanning(self):
        """Stop real-time scanning"""
        if self.scan_task and not self.scan_task.done():
            self.scan_task.cancel()
        self.is_running = False
        self.logger.info("[STOP] Real-time Lee Method scanner stopped")
    
    async def _scan_loop(self):
        """Main scanning loop"""
        while self.is_running:
            try:
                scan_start = datetime.now()
                
                # Perform Lee Method scan
                await self._perform_lee_method_scan()
                
                # Update performance metrics
                scan_duration = (datetime.now() - scan_start).total_seconds()
                self._update_performance_metrics(scan_duration)
                
                # Wait for next scan
                await asyncio.sleep(self.scan_interval)
                
            except Exception as e:
                self.logger.error(f"Error in Lee Method scan loop: {e}")
                self.error_count += 1
                await asyncio.sleep(30)  # Wait before retrying
    
    async def _perform_lee_method_scan(self):
        """Perform Lee Method scan on all symbols"""
        try:
            self.scan_count += 1
            self.last_scan_time = datetime.now()
            
            # Clear old signals (older than 1 hour)
            self._cleanup_old_signals()
            
            # Scan symbols in batches to avoid overwhelming APIs
            batch_size = 5
            new_signals = []
            
            for i in range(0, len(self.scan_symbols), batch_size):
                batch = self.scan_symbols[i:i + batch_size]
                batch_results = await self._scan_batch(batch)
                new_signals.extend(batch_results)
                
                # Small delay between batches
                await asyncio.sleep(0.5)
            
            # Update active signals
            for signal in new_signals:
                self.active_signals[signal.symbol] = signal
            
            # Log scan results
            if new_signals:
                self.logger.info(f"[SIGNAL] Found {len(new_signals)} Lee Method signals")
                for signal in new_signals:
                    self.logger.info(
                        f"   [TARGET] {signal.symbol}: {signal.signal_type} "
                        f"(Confidence: {signal.confidence:.1%})"
                    )
            else:
                self.logger.info("[SCAN] No Lee Method signals detected this scan")
                
        except Exception as e:
            self.logger.error(f"Error performing Lee Method scan: {e}")
            self.error_count += 1
    
    async def _scan_batch(self, symbols: List[str]) -> List[LeeMethodSignal]:
        """Scan a batch of symbols"""
        signals = []
        
        try:
            # Create tasks for concurrent processing
            tasks = [self.lee_scanner.scan_symbol(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for symbol, result in zip(symbols, results):
                if isinstance(result, LeeMethodSignal):
                    signals.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Error scanning {symbol}: {result}")
                # None results are normal (no signal found)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error in batch scan: {e}")
            return signals
    
    def _cleanup_old_signals(self):
        """Remove signals older than 1 hour"""
        try:
            current_time = datetime.now()
            expired_symbols = []
            
            for symbol, signal in self.active_signals.items():
                if (current_time - signal.timestamp).total_seconds() > 3600:  # 1 hour
                    expired_symbols.append(symbol)
            
            for symbol in expired_symbols:
                del self.active_signals[symbol]
                
            if expired_symbols:
                self.logger.info(f"[CLEANUP] Removed {len(expired_symbols)} expired signals")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old signals: {e}")
    
    def _update_performance_metrics(self, scan_duration: float):
        """Update performance tracking metrics"""
        self.scan_times.append(scan_duration)
        
        # Keep only last 100 scan times
        if len(self.scan_times) > 100:
            self.scan_times = self.scan_times[-100:]
    
    def get_latest_signals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the latest Lee Method signals"""
        try:
            # Sort signals by timestamp (newest first)
            sorted_signals = sorted(
                self.active_signals.values(),
                key=lambda x: x.timestamp,
                reverse=True
            )
            
            # Convert to dictionaries and limit results
            return [signal.to_dict() for signal in sorted_signals[:limit]]
            
        except Exception as e:
            self.logger.error(f"Error getting latest signals: {e}")
            return []
    
    def get_scanner_status(self) -> Dict[str, Any]:
        """Get current scanner status"""
        try:
            avg_scan_time = sum(self.scan_times) / len(self.scan_times) if self.scan_times else 0
            
            return {
                'is_running': self.is_running,
                'scan_count': self.scan_count,
                'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
                'active_signals_count': len(self.active_signals),
                'error_count': self.error_count,
                'average_scan_time': round(avg_scan_time, 2),
                'symbols_monitored': len(self.scan_symbols)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting scanner status: {e}")
            return {'is_running': False, 'error': str(e)}
    
    def get_signal_by_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get signal for a specific symbol"""
        try:
            signal = self.active_signals.get(symbol.upper())
            return signal.to_dict() if signal else None
            
        except Exception as e:
            self.logger.error(f"Error getting signal for {symbol}: {e}")
            return None

# Global scanner instance
_scanner_instance = None

def get_scanner_instance(fmp_api_key: str = None) -> AtlasLeeMethodRealtimeScanner:
    """Get or create the global scanner instance"""
    global _scanner_instance
    if _scanner_instance is None:
        _scanner_instance = AtlasLeeMethodRealtimeScanner(fmp_api_key)
    return _scanner_instance

async def test_lee_method_realtime_scanner():
    """Test the real-time Lee Method scanner"""
    print("[SEARCH] Testing A.T.L.A.S. Lee Method Real-time Scanner")
    print("=" * 60)
    
    scanner = AtlasLeeMethodRealtimeScanner(settings.FMP_API_KEY)
    
    # Test single scan
    print("\n[DATA] Performing single scan...")
    await scanner._perform_lee_method_scan()
    
    # Get results
    signals = scanner.get_latest_signals()
    status = scanner.get_scanner_status()
    
    print(f"\n[STATUS] Scanner Status:")
    print(f"   [INFO] Scan Count: {status['scan_count']}")
    print(f"   [INFO] Active Signals: {status['active_signals_count']}")
    print(f"   [INFO] Symbols Monitored: {status['symbols_monitored']}")
    
    if signals:
        print(f"\n[SIGNAL] Latest Lee Method Signals:")
        for signal in signals[:5]:  # Show top 5
            print(f"   [TARGET] {signal['symbol']}: {signal['signal_type']}")
            print(f"      [STAR] Confidence: {signal['confidence']:.1%}")
            print(f"      [MONEY] Entry: ${signal['entry_price']:.2f}")
            print(f"      [UP] Trends: {signal['weekly_trend']}/{signal['daily_trend']}")
    else:
        print("\n[CLOCK] No Lee Method signals detected")

if __name__ == "__main__":
    asyncio.run(test_lee_method_realtime_scanner())
